// Icons Component
// Reusable icon components for home feature

import React from 'react';

interface IconProps {
  className?: string;
}

// Social Media Icons
export const TwitterIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
);

export const FacebookIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

export const InstagramIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

export const PinterestIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.342-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
  </svg>
);

// Payment Method Icons - Simple and Clean Design
export const VisaIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <div className={`${className} bg-gradient-to-r from-blue-600 to-blue-800 rounded-md flex items-center justify-center shadow-sm`}>
    <span className="text-white font-bold text-sm tracking-wider">VISA</span>
  </div>
);

export const MastercardIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <div className={`${className} bg-gradient-to-r from-red-500 to-orange-500 rounded-md flex items-center justify-center shadow-sm relative overflow-hidden`}>
    <div className="absolute left-2 w-4 h-4 bg-red-600 rounded-full opacity-80"></div>
    <div className="absolute right-2 w-4 h-4 bg-orange-400 rounded-full opacity-80"></div>
    <span className="text-white font-bold text-xs z-10">MC</span>
  </div>
);

export const PayPalIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <div className={`${className} bg-gradient-to-r from-blue-700 to-blue-500 rounded-md flex items-center justify-center shadow-sm`}>
    <span className="text-white font-bold text-xs">PayPal</span>
  </div>
);

export const ApplePayIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <div className={`${className} bg-black rounded-md flex items-center justify-center shadow-sm space-x-1`}>
    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
    </svg>
    <span className="text-white font-medium text-xs">Pay</span>
  </div>
);

export const GooglePayIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <div className={`${className} bg-white border border-gray-200 rounded-md flex items-center justify-center shadow-sm space-x-1`}>
    <svg className="w-3 h-3" viewBox="0 0 24 24">
      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
    </svg>
    <span className="text-gray-700 font-medium text-xs">Pay</span>
  </div>
);

// Helper function to get social icon by platform
export const getSocialIcon = (platform: string, className?: string) => {
  switch (platform) {
    case 'Twitter':
      return <TwitterIcon className={className} />;
    case 'Facebook':
      return <FacebookIcon className={className} />;
    case 'Instagram':
      return <InstagramIcon className={className} />;
    case 'Pinterest':
      return <PinterestIcon className={className} />;
    default:
      return <div className={className}>📱</div>;
  }
};

// Helper function to get payment icon by ID
export const getPaymentIcon = (paymentId: string, className?: string) => {
  const iconClassName = className || "w-12 h-8";

  switch (paymentId) {
    case 'visa':
      return <VisaIcon className={iconClassName} />;
    case 'mastercard':
      return <MastercardIcon className={iconClassName} />;
    case 'paypal':
      return <PayPalIcon className={iconClassName} />;
    case 'apple-pay':
      return <ApplePayIcon className={iconClassName} />;
    case 'google-pay':
      return <GooglePayIcon className={iconClassName} />;
    default:
      return (
        <div className={`${iconClassName} bg-gray-100 border border-gray-200 rounded-md flex items-center justify-center`}>
          <span className="text-xs font-semibold text-gray-600">
            {paymentId.slice(0, 4).toUpperCase()}
          </span>
        </div>
      );
  }
};

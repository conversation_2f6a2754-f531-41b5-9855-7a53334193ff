// Icons Component
// Reusable icon components for home feature

import React from 'react';

interface IconProps {
  className?: string;
}

// Social Media Icons
export const TwitterIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
);

export const FacebookIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

export const InstagramIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
);

export const PinterestIcon: React.FC<IconProps> = ({ className = "w-4 h-4" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.342-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
  </svg>
);

// Payment Method Icons - Redesigned for better appearance
export const VisaIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <svg className={className} viewBox="0 0 48 32" fill="none">
    <rect width="48" height="32" rx="6" fill="white" stroke="#E5E7EB" strokeWidth="1"/>
    <rect x="4" y="6" width="40" height="20" rx="3" fill="#1A1F71"/>
    <path d="M14.5 12h2.8l-1.4 8h-2.8l1.4-8zm6.8 5.1c0-2.1-2.9-2.2-2.9-3.2 0-.3.3-.6.9-.7.3 0 1.1-.1 2 .4l.4-1.7c-.5-.2-1.2-.4-2-.4-2.1 0-3.6 1.1-3.6 2.7 0 1.2 1.1 1.8 1.9 2.2.8.4 1.1.7 1.1 1 0 .5-.7.8-1.3.8-1.1 0-1.7-.3-2.2-.5l-.4 1.8c.5.2 1.4.4 2.4.4 2.2 0 3.7-1.1 3.7-2.8zm5.7-5.1h-1.7c-.5 0-.9.1-1.1.7l-3.2 7.3h2.2s.4-1 .4-1.2h2.7c.1.3.3 1.2.3 1.2h2l-1.6-8zm-2.4 5.2c.2-.5.8-2.3.8-2.3l.3-.7.1.7s.4 1.9.5 2.3h-1.7zm-8.9-5.2l-2.2 5.5-.2-1.2c-.4-1.4-1.7-2.8-3.1-3.6l2 7.3h2.3l3.3-8h-2.1z" fill="white"/>
  </svg>
);

export const MastercardIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <svg className={className} viewBox="0 0 48 32" fill="none">
    <rect width="48" height="32" rx="6" fill="white" stroke="#E5E7EB" strokeWidth="1"/>
    <circle cx="18" cy="16" r="7" fill="#EB001B"/>
    <circle cx="30" cy="16" r="7" fill="#F79E1B"/>
    <path d="M24 9c1.86 0 3.55.74 4.78 1.93A6.98 6.98 0 0024 23a6.98 6.98 0 00-4.78-14.07A6.98 6.98 0 0024 9z" fill="#FF5F00"/>
    <text x="24" y="28" textAnchor="middle" fill="#000" fontSize="4" fontFamily="Arial">mastercard</text>
  </svg>
);

export const PayPalIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <svg className={className} viewBox="0 0 48 32" fill="none">
    <rect width="48" height="32" rx="6" fill="white" stroke="#E5E7EB" strokeWidth="1"/>
    <g transform="translate(6, 8)">
      <path d="M7 0h6c3 0 5 2 5 4.8 0 3.5-2.4 6.2-5.8 6.2H9.4L8.5 16H5.5L7 0z" fill="#003087"/>
      <path d="M11 2.5h6c3 0 5 2 5 4.8 0 3.5-2.4 6.2-5.8 6.2h-2.8l-.9 4.5h-2.5L11 2.5z" fill="#009CDE"/>
      <text x="18" y="14" fontSize="5" fill="#003087" fontFamily="Arial">PayPal</text>
    </g>
  </svg>
);

export const ApplePayIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <svg className={className} viewBox="0 0 48 32" fill="none">
    <rect width="48" height="32" rx="6" fill="white" stroke="#E5E7EB" strokeWidth="1"/>
    <g transform="translate(8, 8)">
      <path d="M7.5 2c-.3-.8.1-1.6.6-2.1.5-.6 1.4-1 2.2-.9.1.8-.3 1.6-.7 2.1-.5.6-1.3 1-2.1.9zm2.1 1.1c-1.2-.1-2.3.7-2.9.7-.6 0-1.5-.6-2.5-.6-1.2.1-2.4.7-3 1.9-1.3 2.3-.4 5.7.9 7.6.6.9 1.3 1.9 2.3 1.9.9-.1 1.2-.5 2.3-.5 1.1 0 1.3.5 2.3.5 1-.1 1.6-.9 2.2-1.8.7-1 1-2 1-2.1-.1-.1-1.8-.7-1.9-2.8-.1-1.7 1.3-2.5 1.4-2.6-.8-1.2-2-1.3-2.4-1.3z" fill="#000"/>
      <text x="16" y="12" fontSize="6" fill="#000" fontFamily="Arial">Pay</text>
    </g>
  </svg>
);

export const GooglePayIcon: React.FC<IconProps> = ({ className = "w-12 h-8" }) => (
  <svg className={className} viewBox="0 0 48 32" fill="none">
    <rect width="48" height="32" rx="6" fill="white" stroke="#E5E7EB" strokeWidth="1"/>
    <g transform="translate(6, 8)">
      <path d="M15.5 8c-2.2 0-4 1.8-4 4s1.8 4 4 4c1.1 0 2.1-.4 2.8-1.2l-1.1-1.1c-.4.5-1 .8-1.7.8-1.3 0-2.4-1.1-2.4-2.5s1.1-2.5 2.4-2.5c.9 0 1.7.4 2.1 1h-2.1v1.5h3.8c0-.2.1-.4.1-.6 0-2.2-1.8-4-4-4z" fill="#4285F4"/>
      <text x="18" y="14" fontSize="5" fill="#5F6368" fontFamily="Arial">Pay</text>
    </g>
  </svg>
);

// Helper function to get social icon by platform
export const getSocialIcon = (platform: string, className?: string) => {
  switch (platform) {
    case 'Twitter':
      return <TwitterIcon className={className} />;
    case 'Facebook':
      return <FacebookIcon className={className} />;
    case 'Instagram':
      return <InstagramIcon className={className} />;
    case 'Pinterest':
      return <PinterestIcon className={className} />;
    default:
      return <div className={className}>📱</div>;
  }
};

// Helper function to get payment icon by ID
export const getPaymentIcon = (paymentId: string, className?: string) => {
  switch (paymentId) {
    case 'visa':
      return <VisaIcon className={className} />;
    case 'mastercard':
      return <MastercardIcon className={className} />;
    case 'paypal':
      return <PayPalIcon className={className} />;
    case 'apple-pay':
      return <ApplePayIcon className={className} />;
    case 'google-pay':
      return <GooglePayIcon className={className} />;
    default:
      return (
        <span className="text-xs font-semibold text-gray-600">
          {paymentId.slice(0, 4).toUpperCase()}
        </span>
      );
  }
};

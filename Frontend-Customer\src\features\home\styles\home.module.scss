// Home Feature Styles
// SCSS module for home page specific styling

.homeContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.promotionalBanner {
  background-color: #000;
  color: white;
  text-align: center;
  padding: 0.5rem 1rem;
  position: relative;
  
  .bannerText {
    font-size: 0.875rem;
    
    .signUpLink {
      text-decoration: underline;
      font-weight: 500;
      
      &:hover {
        color: #e5e5e5;
      }
    }
  }
  
  .closeButton {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    
    &:hover {
      color: #d1d5db;
    }
  }
}

.header {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  
  .headerContent {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 1rem;
    
    @media (min-width: 640px) {
      padding: 0 1.5rem;
    }
    
    @media (min-width: 1024px) {
      padding: 0 2rem;
    }
  }
  
  .headerRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
  }
}

.logo {
  flex-shrink: 0;
  
  .logoLink {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    text-decoration: none;
    
    &:hover {
      color: #374151;
    }
  }
}

.navigation {
  display: none;
  
  @media (min-width: 768px) {
    display: flex;
    gap: 2rem;
  }
  
  .navItem {
    position: relative;
    
    .navButton {
      color: #111827;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      font-weight: 500;
      background: none;
      border: none;
      cursor: pointer;
      transition: color 0.2s;
      
      &:hover {
        color: #6b7280;
      }
      
      &.active {
        color: #000;
        font-weight: 600;
      }
    }
    
    .dropdown {
      position: absolute;
      left: 0;
      margin-top: 0.5rem;
      width: 12rem;
      background-color: white;
      border-radius: 0.375rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s;
      z-index: 50;
      
      .dropdownContent {
        padding: 0.25rem 0;
        
        .dropdownItem {
          display: block;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
          color: #374151;
          text-decoration: none;
          
          &:hover {
            background-color: #f9fafb;
          }
        }
      }
    }
    
    &:hover .dropdown {
      opacity: 1;
      visibility: visible;
    }
  }
}

.searchContainer {
  flex: 1;
  max-width: 32rem;
  margin: 0 2rem;
  
  .searchForm {
    position: relative;
    
    .searchInput {
      width: 100%;
      padding: 0.5rem 1rem 0.5rem 2.5rem;
      border: 1px solid #d1d5db;
      border-radius: 9999px;
      background-color: #f9fafb;
      
      &:focus {
        outline: none;
        ring: 2px;
        ring-color: #000;
        border-color: transparent;
      }
    }
    
    .searchIcon {
      position: absolute;
      top: 50%;
      left: 0.75rem;
      transform: translateY(-50%);
      width: 1.25rem;
      height: 1.25rem;
      color: #9ca3af;
      pointer-events: none;
    }
    
    .clearButton {
      position: absolute;
      top: 50%;
      right: 0.75rem;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      
      .clearIcon {
        width: 1.25rem;
        height: 1.25rem;
        color: #9ca3af;
        
        &:hover {
          color: #6b7280;
        }
      }
    }
  }
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 1rem;
  
  .actionLink {
    color: #111827;
    text-decoration: none;
    
    &:hover {
      color: #6b7280;
    }
    
    .actionIcon {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

.mobileMenuButton {
  display: block;
  padding: 0.5rem;
  background: none;
  border: none;
  color: #111827;
  cursor: pointer;
  
  @media (min-width: 768px) {
    display: none;
  }
  
  &:hover {
    color: #6b7280;
  }
  
  .menuIcon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

.mobileNavigation {
  display: block;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  
  @media (min-width: 768px) {
    display: none;
  }
  
  .mobileNavContent {
    padding: 0.5rem 0.5rem 0.75rem;
    
    .mobileNavItem {
      display: block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      font-weight: 500;
      color: #111827;
      text-decoration: none;
      
      &:hover {
        color: #6b7280;
      }
    }
  }
}

.errorMessage {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #b91c1c;
  padding: 0.75rem 1rem;
  position: relative;
  
  .errorText {
    display: block;
    
    @media (min-width: 640px) {
      display: inline;
    }
  }
  
  .errorCloseButton {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    cursor: pointer;
    
    .srOnly {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
  }
}

.mainContent {
  flex: 1;
  
  .loadingContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 16rem;
    
    .loadingSpinner {
      animation: spin 1s linear infinite;
      border-radius: 50%;
      width: 3rem;
      height: 3rem;
      border: 2px solid transparent;
      border-bottom-color: #000;
    }
  }
  
  .contentContainer {
    max-width: 80rem;
    margin: 0 auto;
    padding: 2rem 1rem;
    
    @media (min-width: 640px) {
      padding: 2rem 1.5rem;
    }
    
    @media (min-width: 1024px) {
      padding: 2rem 2rem;
    }
  }
  
  .heroSection {
    text-align: center;
    
    .heroTitle {
      font-size: 2.25rem;
      font-weight: 700;
      color: #111827;
      margin-bottom: 1rem;
    }
    
    .heroSubtitle {
      font-size: 1.25rem;
      color: #6b7280;
      margin-bottom: 2rem;
    }
    
    .heroActions {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      max-width: 28rem;
      margin: 0 auto;
      
      .primaryButton {
        display: block;
        width: 100%;
        background-color: #000;
        color: white;
        text-align: center;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 500;
        text-decoration: none;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #374151;
        }
      }
      
      .secondaryButton {
        display: block;
        width: 100%;
        border: 1px solid #000;
        color: #000;
        background-color: transparent;
        text-align: center;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 500;
        text-decoration: none;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #f9fafb;
        }
      }
    }
  }
}

.footer {
  background-color: #f3f4f6;
  border-top: 1px solid #e5e7eb;
  
  .footerContent {
    max-width: 80rem;
    margin: 0 auto;
    padding: 3rem 1rem;
    
    @media (min-width: 640px) {
      padding: 3rem 1.5rem;
    }
    
    @media (min-width: 1024px) {
      padding: 3rem 2rem;
    }
  }
  
  .footerGrid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    
    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  .companySection {
    @media (min-width: 768px) {
      grid-column: span 1;
    }
    
    .companyName {
      font-size: 1.5rem;
      font-weight: 700;
      color: #000;
      margin-bottom: 1rem;
    }
    
    .companyDescription {
      color: #6b7280;
      margin-bottom: 1.5rem;
      font-size: 0.875rem;
      line-height: 1.5;
    }
    
    .socialLinks {
      display: flex;
      gap: 1rem;
      
      .socialLink {
        width: 2rem;
        height: 2rem;
        background-color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        text-decoration: none;
        transition: color 0.2s;
        
        &:hover {
          color: #000;
        }
      }
    }
  }
  
  .footerSection {
    @media (min-width: 768px) {
      grid-column: span 1;
    }
    
    .sectionTitle {
      font-size: 0.875rem;
      font-weight: 600;
      color: #111827;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 1rem;
    }
    
    .sectionLinks {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .sectionLink {
        color: #6b7280;
        font-size: 0.875rem;
        text-decoration: none;
        transition: color 0.2s;
        
        &:hover {
          color: #111827;
        }
      }
    }
  }
  
  .footerBottom {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    @media (min-width: 768px) {
      flex-direction: row;
      justify-content: space-between;
    }
    
    .copyright {
      color: #6b7280;
      font-size: 0.875rem;
      margin-bottom: 1rem;
      
      @media (min-width: 768px) {
        margin-bottom: 0;
      }
    }
    
    .paymentMethods {
      display: flex;
      gap: 1rem;
      
      .paymentMethod {
        width: 3rem;
        height: 2rem;
        background-color: white;
        border-radius: 0.25rem;
        border: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .paymentIcon {
          font-size: 0.75rem;
          font-weight: 600;
          color: #6b7280;
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

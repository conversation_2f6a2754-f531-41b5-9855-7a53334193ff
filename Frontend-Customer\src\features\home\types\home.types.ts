// Home Feature Types
// TypeScript definitions for home page components and state

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  isActive?: boolean;
  hasDropdown?: boolean;
  dropdownItems?: NavigationItem[];
}

export interface NavigationState {
  items: NavigationItem[];
  activeItem: string | null;
  isMenuOpen: boolean;
}

// Search Types
export interface SearchState {
  query: string;
  isSearching: boolean;
  suggestions: string[];
  recentSearches: string[];
}

export interface SearchRequest {
  query: string;
  filters?: SearchFilters;
}

export interface SearchFilters {
  category?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  brand?: string;
  size?: string;
  color?: string;
}

// Footer Types
export interface FooterLink {
  id: string;
  label: string;
  href: string;
}

export interface FooterSection {
  id: string;
  title: string;
  links: FooterLink[];
}

export interface SocialLink {
  id: string;
  platform: string;
  href: string;
  icon: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
}

export interface FooterData {
  sections: FooterSection[];
  socialLinks: SocialLink[];
  paymentMethods: PaymentMethod[];
  companyInfo: {
    name: string;
    description: string;
    copyright: string;
  };
}

// Home Page State
export interface HomeState {
  navigation: NavigationState;
  search: SearchState;
  footer: FooterData;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}

// API Response Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// Component Props Types
export interface HomeContainerProps {
  className?: string;
  onNavigationChange?: (activeItem: string) => void;
  onSearch?: (query: string) => void;
}

export interface HomePresenterProps {
  navigation: NavigationState;
  search: SearchState;
  footer: FooterData;
  isLoading: boolean;
  error: string | null;
  onNavigationItemClick: (itemId: string) => void;
  onMenuToggle: () => void;
  onSearchChange: (query: string) => void;
  onSearchSubmit: (query: string) => void;
  onSearchClear: () => void;
  onClearError: () => void;
}

export interface HomeCallStateProps {
  children: (props: {
    navigation: NavigationState;
    search: SearchState;
    footer: FooterData;
    isLoading: boolean;
    error: string | null;
    setActiveNavigation: (itemId: string) => void;
    toggleMenu: () => void;
    updateSearch: (query: string) => void;
    submitSearch: (query: string) => void;
    clearSearch: () => void;
    clearError: () => void;
    initializeHome: () => void;
  }) => React.ReactNode;
}

// Action Types
export interface SetActiveNavigationAction {
  type: 'home/setActiveNavigation';
  payload: string;
}

export interface ToggleMenuAction {
  type: 'home/toggleMenu';
}

export interface UpdateSearchAction {
  type: 'home/updateSearch';
  payload: string;
}

export interface SubmitSearchAction {
  type: 'home/submitSearch';
  payload: SearchRequest;
}

export interface ClearSearchAction {
  type: 'home/clearSearch';
}

export interface SetLoadingAction {
  type: 'home/setLoading';
  payload: boolean;
}

export interface SetErrorAction {
  type: 'home/setError';
  payload: string | null;
}

export interface ClearErrorAction {
  type: 'home/clearError';
}

export interface InitializeHomeAction {
  type: 'home/initializeHome';
}

export interface InitializeHomeSuccessAction {
  type: 'home/initializeHomeSuccess';
  payload: {
    navigation: NavigationItem[];
    footer: FooterData;
  };
}

export interface InitializeHomeFailureAction {
  type: 'home/initializeHomeFailure';
  payload: ApiError;
}

// Union type for all actions
export type HomeAction = 
  | SetActiveNavigationAction
  | ToggleMenuAction
  | UpdateSearchAction
  | SubmitSearchAction
  | ClearSearchAction
  | SetLoadingAction
  | SetErrorAction
  | ClearErrorAction
  | InitializeHomeAction
  | InitializeHomeSuccessAction
  | InitializeHomeFailureAction;

{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomeContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeContainer() from the server but HomeContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomeContainer.tsx <module evaluation>\",\n    \"HomeContainer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gFACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomeContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeContainer() from the server but HomeContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomeContainer.tsx\",\n    \"HomeContainer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4DACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomePresenter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePresenter = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePresenter() from the server but HomePresenter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomePresenter.tsx <module evaluation>\",\n    \"HomePresenter\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gFACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomePresenter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePresenter = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePresenter() from the server but HomePresenter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomePresenter.tsx\",\n    \"HomePresenter\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4DACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/Icons.tsx"], "sourcesContent": ["// Icons Component\n// Reusable icon components for home feature\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n}\n\n// Social Media Icons\nexport const TwitterIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n  </svg>\n);\n\nexport const FacebookIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n  </svg>\n);\n\nexport const InstagramIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n  </svg>\n);\n\nexport const PinterestIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.342-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n  </svg>\n);\n\n// Payment Method Icons - Simple and Clean Design\nexport const VisaIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-blue-600 to-blue-800 rounded-md flex items-center justify-center shadow-sm`}>\n    <span className=\"text-white font-bold text-sm tracking-wider\">VISA</span>\n  </div>\n);\n\nexport const MastercardIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-red-500 to-orange-500 rounded-md flex items-center justify-center shadow-sm relative overflow-hidden`}>\n    <div className=\"absolute left-2 w-4 h-4 bg-red-600 rounded-full opacity-80\"></div>\n    <div className=\"absolute right-2 w-4 h-4 bg-orange-400 rounded-full opacity-80\"></div>\n    <span className=\"text-white font-bold text-xs z-10\">MC</span>\n  </div>\n);\n\nexport const PayPalIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-blue-700 to-blue-500 rounded-md flex items-center justify-center shadow-sm`}>\n    <span className=\"text-white font-bold text-xs\">PayPal</span>\n  </div>\n);\n\nexport const ApplePayIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-black rounded-md flex items-center justify-center shadow-sm space-x-1`}>\n    <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n    </svg>\n    <span className=\"text-white font-medium text-xs\">Pay</span>\n  </div>\n);\n\nexport const GooglePayIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-white border border-gray-200 rounded-md flex items-center justify-center shadow-sm space-x-1`}>\n    <svg className=\"w-3 h-3\" viewBox=\"0 0 24 24\">\n      <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" fill=\"#4285F4\"/>\n      <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" fill=\"#34A853\"/>\n      <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\" fill=\"#FBBC05\"/>\n      <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" fill=\"#EA4335\"/>\n    </svg>\n    <span className=\"text-gray-700 font-medium text-xs\">Pay</span>\n  </div>\n);\n\n// Helper function to get social icon by platform\nexport const getSocialIcon = (platform: string, className?: string) => {\n  switch (platform) {\n    case 'Twitter':\n      return <TwitterIcon className={className} />;\n    case 'Facebook':\n      return <FacebookIcon className={className} />;\n    case 'Instagram':\n      return <InstagramIcon className={className} />;\n    case 'Pinterest':\n      return <PinterestIcon className={className} />;\n    default:\n      return <div className={className}>📱</div>;\n  }\n};\n\n// Helper function to get payment icon by ID\nexport const getPaymentIcon = (paymentId: string, className?: string) => {\n  const iconClassName = className || \"w-12 h-8\";\n\n  switch (paymentId) {\n    case 'visa':\n      return <VisaIcon className={iconClassName} />;\n    case 'mastercard':\n      return <MastercardIcon className={iconClassName} />;\n    case 'paypal':\n      return <PayPalIcon className={iconClassName} />;\n    case 'apple-pay':\n      return <ApplePayIcon className={iconClassName} />;\n    case 'google-pay':\n      return <GooglePayIcon className={iconClassName} />;\n    default:\n      return (\n        <div className={`${iconClassName} bg-gray-100 border border-gray-200 rounded-md flex items-center justify-center`}>\n          <span className=\"text-xs font-semibold text-gray-600\">\n            {paymentId.slice(0, 4).toUpperCase()}\n          </span>\n        </div>\n      );\n  }\n};\n"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,4CAA4C;;;;;;;;;;;;;;;;AASrC,MAAM,cAAmC,CAAC,EAAE,YAAY,SAAS,EAAE,iBACxE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,eAAoC,CAAC,EAAE,YAAY,SAAS,EAAE,iBACzE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,gBAAqC,CAAC,EAAE,YAAY,SAAS,EAAE,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,gBAAqC,CAAC,EAAE,YAAY,SAAS,EAAE,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAKL,MAAM,WAAgC,CAAC,EAAE,YAAY,UAAU,EAAE,iBACtE,8OAAC;QAAI,WAAW,GAAG,UAAU,iGAAiG,CAAC;kBAC7H,cAAA,8OAAC;YAAK,WAAU;sBAA8C;;;;;;;;;;;AAI3D,MAAM,iBAAsC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC5E,8OAAC;QAAI,WAAW,GAAG,UAAU,2HAA2H,CAAC;;0BACvJ,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAK,WAAU;0BAAoC;;;;;;;;;;;;AAIjD,MAAM,aAAkC,CAAC,EAAE,YAAY,UAAU,EAAE,iBACxE,8OAAC;QAAI,WAAW,GAAG,UAAU,iGAAiG,CAAC;kBAC7H,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAI5C,MAAM,eAAoC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC1E,8OAAC;QAAI,WAAW,GAAG,UAAU,yEAAyE,CAAC;;0BACrG,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAe,SAAQ;0BAC9D,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;0BAEV,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAI9C,MAAM,gBAAqC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC3E,8OAAC;QAAI,WAAW,GAAG,UAAU,gGAAgG,CAAC;;0BAC5H,8OAAC;gBAAI,WAAU;gBAAU,SAAQ;;kCAC/B,8OAAC;wBAAK,GAAE;wBAA0H,MAAK;;;;;;kCACvI,8OAAC;wBAAK,GAAE;wBAAwI,MAAK;;;;;;kCACrJ,8OAAC;wBAAK,GAAE;wBAAgI,MAAK;;;;;;kCAC7I,8OAAC;wBAAK,GAAE;wBAAsI,MAAK;;;;;;;;;;;;0BAErJ,8OAAC;gBAAK,WAAU;0BAAoC;;;;;;;;;;;;AAKjD,MAAM,gBAAgB,CAAC,UAAkB;IAC9C,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;gBAAY,WAAW;;;;;;QACjC,KAAK;YACH,qBAAO,8OAAC;gBAAa,WAAW;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC;YACE,qBAAO,8OAAC;gBAAI,WAAW;0BAAW;;;;;;IACtC;AACF;AAGO,MAAM,iBAAiB,CAAC,WAAmB;IAChD,MAAM,gBAAgB,aAAa;IAEnC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;gBAAS,WAAW;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC;gBAAe,WAAW;;;;;;QACpC,KAAK;YACH,qBAAO,8OAAC;gBAAW,WAAW;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC;gBAAa,WAAW;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC;YACE,qBACE,8OAAC;gBAAI,WAAW,GAAG,cAAc,+EAA+E,CAAC;0BAC/G,cAAA,8OAAC;oBAAK,WAAU;8BACb,UAAU,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;IAI5C;AACF", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeCallState.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomeCallState = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeCallState() from the server but HomeCallState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomeCallState.tsx <module evaluation>\",\n    \"HomeCallState\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4EACA", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeCallState.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomeCallState = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeCallState() from the server but HomeCallState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/home/<USER>/HomeCallState.tsx\",\n    \"HomeCallState\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wDACA", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/mockData.ts"], "sourcesContent": ["import { Banner, Product, ProductCategory } from '../types/home.types';\n\n// Mock Banners\nexport const mockBanners: Banner[] = [\n  {\n    id: '1',\n    title: 'SUMMER CLEARANCE SALE',\n    subtitle: 'MLB',\n    description: 'SALE UP TO 50%',\n    image: '/images/banners/summer-sale.jpg',\n    link: '/sale',\n    buttonText: 'Shop Now',\n    type: 'hero',\n    isActive: true,\n  },\n  {\n    id: '2',\n    title: 'MLB SALE UP TO 50%',\n    subtitle: 'MUA 2 GIẢM 10%\\nMUA 3 GIẢM 15%',\n    description: '26.6 - 13.7',\n    image: '/images/banners/mlb-sale.jpg',\n    link: '/mlb-sale',\n    buttonText: 'Xem ngay',\n    type: 'promotion',\n    isActive: true,\n  },\n];\n\n// Mock Product Categories\nexport const mockProductCategories: ProductCategory[] = [\n  { id: 'clothes', name: 'CLOTHES', slug: 'clothes', isActive: true },\n  { id: 'hat', name: 'H<PERSON>', slug: 'hat', isActive: true },\n  { id: 'shoes', name: '<PERSON>OE<PERSON>', slug: 'shoes', isActive: true },\n  { id: 'bag', name: '<PERSON><PERSON>', slug: 'bag', isActive: true },\n];\n\n// Mock New Arrivals\nexport const mockNewArrivals: Product[] = [\n  {\n    id: '1',\n    name: 'Áo khoác bomber tổ phối oversize varsity',\n    brand: 'MLB',\n    price: 2890000,\n    image: 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop&crop=center'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.5,\n    reviewCount: 128,\n  },\n  {\n    id: '2',\n    name: 'Áo thun unisex cổ tròn tay Faded Varsity',\n    brand: 'MLB',\n    price: 1590000,\n    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center'],\n    category: 'clothes',\n    colors: ['beige', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.3,\n    reviewCount: 89,\n  },\n  {\n    id: '3',\n    name: 'Áo thun unisex cổ tròn tay ngắn varsity',\n    brand: 'MLB',\n    price: 1390000,\n    image: 'https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.7,\n    reviewCount: 156,\n  },\n  {\n    id: '4',\n    name: 'Áo sát nách nữ cổ tròn New York Yankees',\n    brand: 'MLB',\n    price: 1190000,\n    image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.4,\n    reviewCount: 73,\n  },\n];\n\n// Mock Recommended Products\nexport const mockRecommendedProducts: Product[] = [\n  {\n    id: '5',\n    name: 'Nón Snapback phối unisex Kings New York Yankees',\n    brand: 'MLB',\n    price: 1290000,\n    image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=400&h=400&fit=crop&crop=center'],\n    category: 'hat',\n    colors: ['green', 'black'],\n    sizes: ['One Size'],\n    rating: 4.6,\n    reviewCount: 234,\n  },\n  {\n    id: '6',\n    name: 'Giày Sneakers unisex cổ thấp Chunky Runner Classic Monogram',\n    brand: 'MLB',\n    price: 3290000,\n    image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop&crop=center'],\n    category: 'shoes',\n    colors: ['white', 'beige'],\n    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],\n    rating: 4.8,\n    reviewCount: 312,\n  },\n  {\n    id: '7',\n    name: 'Nón bucket unisex Color Denim Unstructured',\n    brand: 'MLB',\n    price: 990000,\n    image: 'https://images.unsplash.com/photo-1576871337622-98d48d1cf531?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1576871337622-98d48d1cf531?w=400&h=400&fit=crop&crop=center'],\n    category: 'hat',\n    colors: ['blue', 'black'],\n    sizes: ['One Size'],\n    rating: 4.2,\n    reviewCount: 98,\n  },\n  {\n    id: '8',\n    name: 'Túi tote chữ kiểu unisex Mega Bear unstructured',\n    brand: 'MLB',\n    price: 1890000,\n    image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop&crop=center'],\n    category: 'bag',\n    colors: ['brown', 'black'],\n    sizes: ['One Size'],\n    rating: 4.5,\n    reviewCount: 167,\n  },\n  {\n    id: '9',\n    name: 'Dép quai ngang unisex New Monogram',\n    brand: 'MLB',\n    price: 1590000,\n    image: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop&crop=center',\n    images: ['https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop&crop=center'],\n    category: 'shoes',\n    colors: ['beige', 'black'],\n    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],\n    rating: 4.3,\n    reviewCount: 145,\n  },\n];\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;CACD;AAGM,MAAM,wBAA2C;IACtD;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM;QAAW,UAAU;IAAK;IAClE;QAAE,IAAI;QAAO,MAAM;QAAO,MAAM;QAAO,UAAU;IAAK;IACtD;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM;QAAS,UAAU;IAAK;IAC5D;QAAE,IAAI;QAAO,MAAM;QAAO,MAAM;QAAO,UAAU;IAAK;CACvD;AAGM,MAAM,kBAA6B;IACxC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAA6F;QACtG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAAgG;QACzG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAAgG;QACzG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAAgG;QACzG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;CACD;AAGM,MAAM,0BAAqC;IAChD;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAAgG;QACzG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAA6F;QACtG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACvD,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAAgG;QACzG,UAAU;QACV,QAAQ;YAAC;YAAQ;SAAQ;QACzB,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAA6F;QACtG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;SAA6F;QACtG,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACvD,QAAQ;QACR,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/homeSlice.ts"], "sourcesContent": ["// Home Redux Slice\n// State management for home page functionality\n\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { RootState } from '@/store';\nimport {\n  HomeState,\n  NavigationItem,\n  FooterData,\n  ApiError,\n  SearchRequest,\n  Banner,\n  Product,\n  ProductCategory\n} from '../types/home.types';\nimport {\n  mockBanners,\n  mockNewArrivals,\n  mockRecommendedProducts,\n  mockProductCategories\n} from '../data/mockData';\n\n// Initial state\nconst initialState: HomeState = {\n  navigation: {\n    items: [\n      {\n        id: 'shop',\n        label: 'Shop',\n        href: '/shop',\n        isActive: false,\n        hasDropdown: true,\n        dropdownItems: [\n          { id: 'women', label: 'Women', href: '/shop/women' },\n          { id: 'men', label: 'Men', href: '/shop/men' },\n          { id: 'accessories', label: 'Accessories', href: '/shop/accessories' },\n        ]\n      },\n      {\n        id: 'on-sale',\n        label: 'On Sale',\n        href: '/sale',\n        isActive: false,\n      },\n      {\n        id: 'new-arrivals',\n        label: 'New Arrivals',\n        href: '/new-arrivals',\n        isActive: false,\n      },\n    ],\n    activeItem: null,\n    isMenuOpen: false,\n  },\n  search: {\n    query: '',\n    isSearching: false,\n    suggestions: [],\n    recentSearches: [],\n  },\n  footer: {\n    sections: [\n      {\n        id: 'help',\n        title: 'HELP',\n        links: [\n          { id: 'customer-support', label: 'Customer Support', href: '/help/support' },\n          { id: 'delivery-details', label: 'Delivery Details', href: '/help/delivery' },\n          { id: 'terms-conditions', label: 'Terms & Conditions', href: '/help/terms' },\n          { id: 'privacy-policy', label: 'Privacy Policy', href: '/help/privacy' },\n        ]\n      },\n      {\n        id: 'faq',\n        title: 'FAQ',\n        links: [\n          { id: 'account', label: 'Account', href: '/faq/account' },\n          { id: 'manage-deliveries', label: 'Manage Deliveries', href: '/faq/deliveries' },\n          { id: 'orders', label: 'Orders', href: '/faq/orders' },\n          { id: 'payments', label: 'Payments', href: '/faq/payments' },\n        ]\n      }\n    ],\n    socialLinks: [\n      { id: 'twitter', platform: 'Twitter', href: '#', icon: 'twitter' },\n      { id: 'facebook', platform: 'Facebook', href: '#', icon: 'facebook' },\n      { id: 'instagram', platform: 'Instagram', href: '#', icon: 'instagram' },\n      { id: 'pinterest', platform: 'Pinterest', href: '#', icon: 'pinterest' },\n    ],\n    paymentMethods: [\n      { id: 'visa', name: 'Visa', icon: 'visa' },\n      { id: 'mastercard', name: 'Mastercard', icon: 'mastercard' },\n      { id: 'paypal', name: 'PayPal', icon: 'paypal' },\n      { id: 'apple-pay', name: 'Apple Pay', icon: 'apple-pay' },\n      { id: 'google-pay', name: 'Google Pay', icon: 'google-pay' },\n    ],\n    companyInfo: {\n      name: 'FIT',\n      description: 'We have clothes that suits your style and which you\\'re proud to wear. From women to men.',\n      copyright: 'FIT © 2025, All Rights Reserved',\n    }\n  },\n  banners: mockBanners,\n  newArrivals: mockNewArrivals,\n  recommendedProducts: mockRecommendedProducts,\n  productCategories: mockProductCategories,\n  isLoading: false,\n  error: null,\n  isInitialized: false,\n};\n\n// Home slice\nconst homeSlice = createSlice({\n  name: 'home',\n  initialState,\n  reducers: {\n    // Navigation actions\n    setActiveNavigation: (state, action: PayloadAction<string>) => {\n      state.navigation.activeItem = action.payload;\n      state.navigation.items = state.navigation.items.map(item => ({\n        ...item,\n        isActive: item.id === action.payload\n      }));\n    },\n\n    toggleMenu: (state) => {\n      state.navigation.isMenuOpen = !state.navigation.isMenuOpen;\n    },\n\n    closeMenu: (state) => {\n      state.navigation.isMenuOpen = false;\n    },\n\n    // Search actions\n    updateSearch: (state, action: PayloadAction<string>) => {\n      state.search.query = action.payload;\n    },\n\n    setSearching: (state, action: PayloadAction<boolean>) => {\n      state.search.isSearching = action.payload;\n    },\n\n    submitSearch: (state, action: PayloadAction<SearchRequest>) => {\n      state.search.isSearching = true;\n      // Add to recent searches if not empty and not already present\n      if (action.payload.query && !state.search.recentSearches.includes(action.payload.query)) {\n        state.search.recentSearches.unshift(action.payload.query);\n        // Keep only last 5 searches\n        state.search.recentSearches = state.search.recentSearches.slice(0, 5);\n      }\n    },\n\n    submitSearchSuccess: (state, action: PayloadAction<string[]>) => {\n      state.search.isSearching = false;\n      state.search.suggestions = action.payload;\n    },\n\n    submitSearchFailure: (state, action: PayloadAction<ApiError>) => {\n      state.search.isSearching = false;\n      state.error = action.payload.message;\n    },\n\n    clearSearch: (state) => {\n      state.search.query = '';\n      state.search.suggestions = [];\n    },\n\n    // General actions\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n\n    clearError: (state) => {\n      state.error = null;\n    },\n\n    // Initialize home\n    initializeHome: (state) => {\n      state.isLoading = true;\n      state.error = null;\n    },\n\n    initializeHomeSuccess: (state, action: PayloadAction<{ navigation: NavigationItem[], footer: FooterData }>) => {\n      state.isLoading = false;\n      state.navigation.items = action.payload.navigation;\n      state.footer = action.payload.footer;\n      state.isInitialized = true;\n    },\n\n    initializeHomeFailure: (state, action: PayloadAction<ApiError>) => {\n      state.isLoading = false;\n      state.error = action.payload.message;\n      state.isInitialized = false;\n    },\n\n    // Product and Banner actions\n    selectProduct: (state, action: PayloadAction<string>) => {\n      // Handle product selection - could navigate to product detail page\n      console.log('Product selected:', action.payload);\n    },\n\n    selectCategory: (state, action: PayloadAction<string>) => {\n      // Handle category selection - could filter products or navigate to category page\n      console.log('Category selected:', action.payload);\n    },\n\n    selectBanner: (state, action: PayloadAction<string>) => {\n      // Handle banner click - could navigate to promotion page\n      console.log('Banner selected:', action.payload);\n    },\n\n    // Reset state\n    resetHomeState: () => initialState,\n  },\n});\n\n// Export actions\nexport const {\n  setActiveNavigation,\n  toggleMenu,\n  closeMenu,\n  updateSearch,\n  setSearching,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  clearSearch,\n  setLoading,\n  setError,\n  clearError,\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  selectProduct,\n  selectCategory,\n  selectBanner,\n  resetHomeState,\n} = homeSlice.actions;\n\n// Action creators object for easier import\nexport const homeActionCreators = homeSlice.actions;\n\n// Selectors\nexport const selectHomeState = (state: RootState) => state.home;\nexport const selectNavigation = (state: RootState) => state.home.navigation;\nexport const selectSearch = (state: RootState) => state.home.search;\nexport const selectFooter = (state: RootState) => state.home.footer;\nexport const selectBanners = (state: RootState) => state.home.banners;\nexport const selectNewArrivals = (state: RootState) => state.home.newArrivals;\nexport const selectRecommendedProducts = (state: RootState) => state.home.recommendedProducts;\nexport const selectProductCategories = (state: RootState) => state.home.productCategories;\nexport const selectIsLoading = (state: RootState) => state.home.isLoading;\nexport const selectError = (state: RootState) => state.home.error;\nexport const selectIsInitialized = (state: RootState) => state.home.isInitialized;\n\n// Export reducer\nexport const homeReducer = homeSlice.reducer;\nexport default homeSlice.reducer;\n"], "names": [], "mappings": "AAAA,mBAAmB;AACnB,+CAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C;AAYA;;;AAOA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,YAAY;QACV,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,eAAe;oBACb;wBAAE,IAAI;wBAAS,OAAO;wBAAS,MAAM;oBAAc;oBACnD;wBAAE,IAAI;wBAAO,OAAO;wBAAO,MAAM;oBAAY;oBAC7C;wBAAE,IAAI;wBAAe,OAAO;wBAAe,MAAM;oBAAoB;iBACtE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,YAAY;QACZ,YAAY;IACd;IACA,QAAQ;QACN,OAAO;QACP,aAAa;QACb,aAAa,EAAE;QACf,gBAAgB,EAAE;IACpB;IACA,QAAQ;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,OAAO;oBACL;wBAAE,IAAI;wBAAoB,OAAO;wBAAoB,MAAM;oBAAgB;oBAC3E;wBAAE,IAAI;wBAAoB,OAAO;wBAAoB,MAAM;oBAAiB;oBAC5E;wBAAE,IAAI;wBAAoB,OAAO;wBAAsB,MAAM;oBAAc;oBAC3E;wBAAE,IAAI;wBAAkB,OAAO;wBAAkB,MAAM;oBAAgB;iBACxE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,OAAO;oBACL;wBAAE,IAAI;wBAAW,OAAO;wBAAW,MAAM;oBAAe;oBACxD;wBAAE,IAAI;wBAAqB,OAAO;wBAAqB,MAAM;oBAAkB;oBAC/E;wBAAE,IAAI;wBAAU,OAAO;wBAAU,MAAM;oBAAc;oBACrD;wBAAE,IAAI;wBAAY,OAAO;wBAAY,MAAM;oBAAgB;iBAC5D;YACH;SACD;QACD,aAAa;YACX;gBAAE,IAAI;gBAAW,UAAU;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACjE;gBAAE,IAAI;gBAAY,UAAU;gBAAY,MAAM;gBAAK,MAAM;YAAW;YACpE;gBAAE,IAAI;gBAAa,UAAU;gBAAa,MAAM;gBAAK,MAAM;YAAY;YACvE;gBAAE,IAAI;gBAAa,UAAU;gBAAa,MAAM;gBAAK,MAAM;YAAY;SACxE;QACD,gBAAgB;YACd;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,MAAM;YAAO;YACzC;gBAAE,IAAI;gBAAc,MAAM;gBAAc,MAAM;YAAa;YAC3D;gBAAE,IAAI;gBAAU,MAAM;gBAAU,MAAM;YAAS;YAC/C;gBAAE,IAAI;gBAAa,MAAM;gBAAa,MAAM;YAAY;YACxD;gBAAE,IAAI;gBAAc,MAAM;gBAAc,MAAM;YAAa;SAC5D;QACD,aAAa;YACX,MAAM;YACN,aAAa;YACb,WAAW;QACb;IACF;IACA,SAAS,2IAAA,CAAA,cAAW;IACpB,aAAa,2IAAA,CAAA,kBAAe;IAC5B,qBAAqB,2IAAA,CAAA,0BAAuB;IAC5C,mBAAmB,2IAAA,CAAA,wBAAqB;IACxC,WAAW;IACX,OAAO;IACP,eAAe;AACjB;AAEA,aAAa;AACb,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,qBAAqB;QACrB,qBAAqB,CAAC,OAAO;YAC3B,MAAM,UAAU,CAAC,UAAU,GAAG,OAAO,OAAO;YAC5C,MAAM,UAAU,CAAC,KAAK,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3D,GAAG,IAAI;oBACP,UAAU,KAAK,EAAE,KAAK,OAAO,OAAO;gBACtC,CAAC;QACH;QAEA,YAAY,CAAC;YACX,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM,UAAU,CAAC,UAAU;QAC5D;QAEA,WAAW,CAAC;YACV,MAAM,UAAU,CAAC,UAAU,GAAG;QAChC;QAEA,iBAAiB;QACjB,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,OAAO;QACrC;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,WAAW,GAAG,OAAO,OAAO;QAC3C;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,8DAA8D;YAC9D,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC,KAAK,GAAG;gBACvF,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,OAAO,CAAC,KAAK;gBACxD,4BAA4B;gBAC5B,MAAM,MAAM,CAAC,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;YACrE;QACF;QAEA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG,OAAO,OAAO;QAC3C;QAEA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,OAAO;QACtC;QAEA,aAAa,CAAC;YACZ,MAAM,MAAM,CAAC,KAAK,GAAG;YACrB,MAAM,MAAM,CAAC,WAAW,GAAG,EAAE;QAC/B;QAEA,kBAAkB;QAClB,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QAEA,kBAAkB;QAClB,gBAAgB,CAAC;YACf,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,SAAS,GAAG;YAClB,MAAM,UAAU,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,UAAU;YAClD,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YACpC,MAAM,aAAa,GAAG;QACxB;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,OAAO;YACpC,MAAM,aAAa,GAAG;QACxB;QAEA,6BAA6B;QAC7B,eAAe,CAAC,OAAO;YACrB,mEAAmE;YACnE,QAAQ,GAAG,CAAC,qBAAqB,OAAO,OAAO;QACjD;QAEA,gBAAgB,CAAC,OAAO;YACtB,iFAAiF;YACjF,QAAQ,GAAG,CAAC,sBAAsB,OAAO,OAAO;QAClD;QAEA,cAAc,CAAC,OAAO;YACpB,yDAAyD;YACzD,QAAQ,GAAG,CAAC,oBAAoB,OAAO,OAAO;QAChD;QAEA,cAAc;QACd,gBAAgB,IAAM;IACxB;AACF;AAGO,MAAM,EACX,mBAAmB,EACnB,UAAU,EACV,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,cAAc,EACd,qBAAqB,EACrB,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,cAAc,EACf,GAAG,UAAU,OAAO;AAGd,MAAM,qBAAqB,UAAU,OAAO;AAG5C,MAAM,kBAAkB,CAAC,QAAqB,MAAM,IAAI;AACxD,MAAM,mBAAmB,CAAC,QAAqB,MAAM,IAAI,CAAC,UAAU;AACpE,MAAM,eAAe,CAAC,QAAqB,MAAM,IAAI,CAAC,MAAM;AAC5D,MAAM,eAAe,CAAC,QAAqB,MAAM,IAAI,CAAC,MAAM;AAC5D,MAAM,gBAAgB,CAAC,QAAqB,MAAM,IAAI,CAAC,OAAO;AAC9D,MAAM,oBAAoB,CAAC,QAAqB,MAAM,IAAI,CAAC,WAAW;AACtE,MAAM,4BAA4B,CAAC,QAAqB,MAAM,IAAI,CAAC,mBAAmB;AACtF,MAAM,0BAA0B,CAAC,QAAqB,MAAM,IAAI,CAAC,iBAAiB;AAClF,MAAM,kBAAkB,CAAC,QAAqB,MAAM,IAAI,CAAC,SAAS;AAClE,MAAM,cAAc,CAAC,QAAqB,MAAM,IAAI,CAAC,KAAK;AAC1D,MAAM,sBAAsB,CAAC,QAAqB,MAAM,IAAI,CAAC,aAAa;AAG1E,MAAM,cAAc,UAAU,OAAO;uCAC7B,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/homeSaga.ts"], "sourcesContent": ["// Home Redux Saga\n// Side effects management for home page functionality\n\nimport { call, put, takeEvery, takeLatest, delay, select } from 'redux-saga/effects';\nimport { PayloadAction } from '@reduxjs/toolkit';\nimport {\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  setSearching,\n} from './homeSlice';\nimport { \n  SearchRequest, \n  NavigationItem, \n  FooterData, \n  ApiError \n} from '../types/home.types';\n\n// Mock API functions (replace with real API calls)\nconst mockApiDelay = () => new Promise(resolve => setTimeout(resolve, 1000));\n\nconst mockInitializeHomeApi = async (): Promise<{ navigation: NavigationItem[], footer: FooterData }> => {\n  await mockApiDelay();\n  \n  // Return mock data - in real app this would come from API\n  return {\n    navigation: [\n      {\n        id: 'shop',\n        label: 'Shop',\n        href: '/shop',\n        isActive: false,\n        hasDropdown: true,\n        dropdownItems: [\n          { id: 'women', label: 'Women', href: '/shop/women' },\n          { id: 'men', label: 'Men', href: '/shop/men' },\n          { id: 'accessories', label: 'Accessories', href: '/shop/accessories' },\n        ]\n      },\n      {\n        id: 'on-sale',\n        label: 'On Sale',\n        href: '/sale',\n        isActive: false,\n      },\n      {\n        id: 'new-arrivals',\n        label: 'New Arrivals',\n        href: '/new-arrivals',\n        isActive: false,\n      },\n    ],\n    footer: {\n      sections: [\n        {\n          id: 'help',\n          title: 'HELP',\n          links: [\n            { id: 'customer-support', label: 'Customer Support', href: '/help/support' },\n            { id: 'delivery-details', label: 'Delivery Details', href: '/help/delivery' },\n            { id: 'terms-conditions', label: 'Terms & Conditions', href: '/help/terms' },\n            { id: 'privacy-policy', label: 'Privacy Policy', href: '/help/privacy' },\n          ]\n        },\n        {\n          id: 'faq',\n          title: 'FAQ',\n          links: [\n            { id: 'account', label: 'Account', href: '/faq/account' },\n            { id: 'manage-deliveries', label: 'Manage Deliveries', href: '/faq/deliveries' },\n            { id: 'orders', label: 'Orders', href: '/faq/orders' },\n            { id: 'payments', label: 'Payments', href: '/faq/payments' },\n          ]\n        }\n      ],\n      socialLinks: [\n        { id: 'twitter', platform: 'Twitter', href: '#', icon: 'twitter' },\n        { id: 'facebook', platform: 'Facebook', href: '#', icon: 'facebook' },\n        { id: 'instagram', platform: 'Instagram', href: '#', icon: 'instagram' },\n        { id: 'pinterest', platform: 'Pinterest', href: '#', icon: 'pinterest' },\n      ],\n      paymentMethods: [\n        { id: 'visa', name: 'Visa', icon: 'visa' },\n        { id: 'mastercard', name: 'Mastercard', icon: 'mastercard' },\n        { id: 'paypal', name: 'PayPal', icon: 'paypal' },\n        { id: 'apple-pay', name: 'Apple Pay', icon: 'apple-pay' },\n        { id: 'google-pay', name: 'Google Pay', icon: 'google-pay' },\n      ],\n      companyInfo: {\n        name: 'FIT',\n        description: 'We have clothes that suits your style and which you\\'re proud to wear. From women to men.',\n        copyright: 'FIT © 2025, All Rights Reserved',\n      }\n    }\n  };\n};\n\nconst mockSearchApi = async (request: SearchRequest): Promise<string[]> => {\n  await mockApiDelay();\n  \n  // Mock search suggestions based on query\n  const mockSuggestions = [\n    'dress', 'shirt', 'pants', 'shoes', 'jacket', 'skirt', 'blouse', 'jeans',\n    'sweater', 'coat', 'boots', 'sneakers', 'accessories', 'bag', 'hat'\n  ];\n  \n  if (!request.query) {\n    return [];\n  }\n  \n  return mockSuggestions.filter(suggestion => \n    suggestion.toLowerCase().includes(request.query.toLowerCase())\n  ).slice(0, 5);\n};\n\n// Saga workers\nfunction* initializeHomeSaga() {\n  try {\n    const data: { navigation: NavigationItem[], footer: FooterData } = yield call(mockInitializeHomeApi);\n    yield put(initializeHomeSuccess(data));\n  } catch (error) {\n    const apiError: ApiError = {\n      message: error instanceof Error ? error.message : 'Failed to initialize home page',\n      code: 'INIT_ERROR'\n    };\n    yield put(initializeHomeFailure(apiError));\n  }\n}\n\nfunction* submitSearchSaga(action: PayloadAction<SearchRequest>) {\n  try {\n    yield put(setSearching(true));\n    \n    // Add small delay for better UX\n    yield delay(300);\n    \n    const suggestions: string[] = yield call(mockSearchApi, action.payload);\n    yield put(submitSearchSuccess(suggestions));\n  } catch (error) {\n    const apiError: ApiError = {\n      message: error instanceof Error ? error.message : 'Search failed',\n      code: 'SEARCH_ERROR'\n    };\n    yield put(submitSearchFailure(apiError));\n  }\n}\n\n// Watcher sagas\nfunction* watchInitializeHome() {\n  yield takeEvery(initializeHome.type, initializeHomeSaga);\n}\n\nfunction* watchSubmitSearch() {\n  yield takeLatest(submitSearch.type, submitSearchSaga);\n}\n\n// Root saga for home feature\nexport function* homeSaga() {\n  yield takeEvery(initializeHome.type, initializeHomeSaga);\n  yield takeLatest(submitSearch.type, submitSearchSaga);\n}\n\nexport default homeSaga;\n"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,sDAAsD;;;;;AAEtD;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAgBA,mDAAmD;AACnD,MAAM,eAAe,IAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAEtE,MAAM,wBAAwB;IAC5B,MAAM;IAEN,0DAA0D;IAC1D,OAAO;QACL,YAAY;YACV;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,eAAe;oBACb;wBAAE,IAAI;wBAAS,OAAO;wBAAS,MAAM;oBAAc;oBACnD;wBAAE,IAAI;wBAAO,OAAO;wBAAO,MAAM;oBAAY;oBAC7C;wBAAE,IAAI;wBAAe,OAAO;wBAAe,MAAM;oBAAoB;iBACtE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,QAAQ;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL;4BAAE,IAAI;4BAAoB,OAAO;4BAAoB,MAAM;wBAAgB;wBAC3E;4BAAE,IAAI;4BAAoB,OAAO;4BAAoB,MAAM;wBAAiB;wBAC5E;4BAAE,IAAI;4BAAoB,OAAO;4BAAsB,MAAM;wBAAc;wBAC3E;4BAAE,IAAI;4BAAkB,OAAO;4BAAkB,MAAM;wBAAgB;qBACxE;gBACH;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL;4BAAE,IAAI;4BAAW,OAAO;4BAAW,MAAM;wBAAe;wBACxD;4BAAE,IAAI;4BAAqB,OAAO;4BAAqB,MAAM;wBAAkB;wBAC/E;4BAAE,IAAI;4BAAU,OAAO;4BAAU,MAAM;wBAAc;wBACrD;4BAAE,IAAI;4BAAY,OAAO;4BAAY,MAAM;wBAAgB;qBAC5D;gBACH;aACD;YACD,aAAa;gBACX;oBAAE,IAAI;oBAAW,UAAU;oBAAW,MAAM;oBAAK,MAAM;gBAAU;gBACjE;oBAAE,IAAI;oBAAY,UAAU;oBAAY,MAAM;oBAAK,MAAM;gBAAW;gBACpE;oBAAE,IAAI;oBAAa,UAAU;oBAAa,MAAM;oBAAK,MAAM;gBAAY;gBACvE;oBAAE,IAAI;oBAAa,UAAU;oBAAa,MAAM;oBAAK,MAAM;gBAAY;aACxE;YACD,gBAAgB;gBACd;oBAAE,IAAI;oBAAQ,MAAM;oBAAQ,MAAM;gBAAO;gBACzC;oBAAE,IAAI;oBAAc,MAAM;oBAAc,MAAM;gBAAa;gBAC3D;oBAAE,IAAI;oBAAU,MAAM;oBAAU,MAAM;gBAAS;gBAC/C;oBAAE,IAAI;oBAAa,MAAM;oBAAa,MAAM;gBAAY;gBACxD;oBAAE,IAAI;oBAAc,MAAM;oBAAc,MAAM;gBAAa;aAC5D;YACD,aAAa;gBACX,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;QACF;IACF;AACF;AAEA,MAAM,gBAAgB,OAAO;IAC3B,MAAM;IAEN,yCAAyC;IACzC,MAAM,kBAAkB;QACtB;QAAS;QAAS;QAAS;QAAS;QAAU;QAAS;QAAU;QACjE;QAAW;QAAQ;QAAS;QAAY;QAAe;QAAO;KAC/D;IAED,IAAI,CAAC,QAAQ,KAAK,EAAE;QAClB,OAAO,EAAE;IACX;IAEA,OAAO,gBAAgB,MAAM,CAAC,CAAA,aAC5B,WAAW,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,KAC3D,KAAK,CAAC,GAAG;AACb;AAEA,eAAe;AACf,UAAU;IACR,IAAI;QACF,MAAM,OAA6D,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE;QAC9E,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,MAAM,WAAqB;YACzB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;QACR;QACA,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE;IAClC;AACF;AAEA,UAAU,iBAAiB,MAAoC;IAC7D,IAAI;QACF,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;QAEvB,gCAAgC;QAChC,MAAM,CAAA,GAAA,+LAAA,CAAA,QAAK,AAAD,EAAE;QAEZ,MAAM,cAAwB,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,OAAO;QACtE,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,MAAM,WAAqB;YACzB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;QACR;QACA,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;IAChC;AACF;AAEA,gBAAgB;AAChB,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,YAAS,AAAD,EAAE,6IAAA,CAAA,iBAAc,CAAC,IAAI,EAAE;AACvC;AAEA,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,6IAAA,CAAA,eAAY,CAAC,IAAI,EAAE;AACtC;AAGO,UAAU;IACf,MAAM,CAAA,GAAA,kMAAA,CAAA,YAAS,AAAD,EAAE,6IAAA,CAAA,iBAAc,CAAC,IAAI,EAAE;IACrC,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,6IAAA,CAAA,eAAY,CAAC,IAAI,EAAE;AACtC;uCAEe", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>"], "sourcesContent": ["// Home Feature Barrel Export\n// Self-contained home module exports\n\n// Export containers\nexport { HomeContainer } from './containers/HomeContainer';\n\n// Export components\nexport { HomePresenter } from './components/HomePresenter';\nexport * from './components/Icons';\n\n// Export states\nexport { HomeCallState } from './states/HomeCallState';\n\n// Export redux\nexport { \n  homeReducer,\n  setActiveNavigation,\n  toggleMenu,\n  closeMenu,\n  updateSearch,\n  setSearching,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  clearSearch,\n  setLoading,\n  setError,\n  clearError,\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  resetHomeState,\n  homeActionCreators,\n  selectHomeState,\n  selectNavigation,\n  selectSearch,\n  selectFooter,\n  selectIsLoading,\n  selectError,\n  selectIsInitialized,\n} from './redux/homeSlice';\n\nexport { homeSaga } from './redux/homeSaga';\n\n// Export types\nexport type {\n  NavigationItem,\n  NavigationState,\n  SearchState,\n  SearchRequest,\n  SearchFilters,\n  FooterLink,\n  FooterSection,\n  SocialLink,\n  PaymentMethod,\n  FooterData,\n  HomeState,\n  ApiError,\n  HomeContainerProps,\n  HomePresenterProps,\n  HomeCallStateProps,\n  SetActiveNavigationAction,\n  ToggleMenuAction,\n  UpdateSearchAction,\n  SubmitSearchAction,\n  ClearSearchAction,\n  SetLoadingAction,\n  SetErrorAction,\n  ClearErrorAction,\n  InitializeHomeAction,\n  InitializeHomeSuccessAction,\n  InitializeHomeFailureAction,\n  HomeAction,\n} from './types/home.types';\n\n// Default export\nexport { HomeContainer as default } from './containers/HomeContainer';\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,qCAAqC;AAErC,oBAAoB;;AACpB;AAEA,oBAAoB;AACpB;AACA;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf;AA4BA", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/app/page.tsx"], "sourcesContent": ["import { HomeContainer } from '@/features/home';\r\n\r\nexport default function Home() {\r\n  return <HomeContainer />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,uJAAA,CAAA,gBAAa;;;;;AACvB", "debugId": null}}]}
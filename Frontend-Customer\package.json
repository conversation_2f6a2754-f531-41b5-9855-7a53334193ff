{"name": "frontend-customer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "clsx": "^2.1.1", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "redux-saga": "^1.3.0", "sass": "^1.89.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/redux-saga": "^0.9.31", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}
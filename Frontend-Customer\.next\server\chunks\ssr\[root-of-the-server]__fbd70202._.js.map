{"version": 3, "sources": [], "sections": [{"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/features/auth/login/styles/login.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkbox\": \"login-module-scss-module__4gE15G__checkbox\",\n  \"checkboxGroup\": \"login-module-scss-module__4gE15G__checkboxGroup\",\n  \"checkboxLabel\": \"login-module-scss-module__4gE15G__checkboxLabel\",\n  \"closeIcon\": \"login-module-scss-module__4gE15G__closeIcon\",\n  \"container\": \"login-module-scss-module__4gE15G__container\",\n  \"demoNote\": \"login-module-scss-module__4gE15G__demoNote\",\n  \"errorAlert\": \"login-module-scss-module__4gE15G__errorAlert\",\n  \"errorClose\": \"login-module-scss-module__4gE15G__errorClose\",\n  \"errorContent\": \"login-module-scss-module__4gE15G__errorContent\",\n  \"errorIcon\": \"login-module-scss-module__4gE15G__errorIcon\",\n  \"errorMessage\": \"login-module-scss-module__4gE15G__errorMessage\",\n  \"errorText\": \"login-module-scss-module__4gE15G__errorText\",\n  \"errorTitle\": \"login-module-scss-module__4gE15G__errorTitle\",\n  \"footer\": \"login-module-scss-module__4gE15G__footer\",\n  \"form\": \"login-module-scss-module__4gE15G__form\",\n  \"formGroup\": \"login-module-scss-module__4gE15G__formGroup\",\n  \"header\": \"login-module-scss-module__4gE15G__header\",\n  \"icon\": \"login-module-scss-module__4gE15G__icon\",\n  \"input\": \"login-module-scss-module__4gE15G__input\",\n  \"label\": \"login-module-scss-module__4gE15G__label\",\n  \"loginCard\": \"login-module-scss-module__4gE15G__loginCard\",\n  \"logoutButton\": \"login-module-scss-module__4gE15G__logoutButton\",\n  \"spin\": \"login-module-scss-module__4gE15G__spin\",\n  \"spinner\": \"login-module-scss-module__4gE15G__spinner\",\n  \"submitButton\": \"login-module-scss-module__4gE15G__submitButton\",\n  \"subtitle\": \"login-module-scss-module__4gE15G__subtitle\",\n  \"successCard\": \"login-module-scss-module__4gE15G__successCard\",\n  \"successIcon\": \"login-module-scss-module__4gE15G__successIcon\",\n  \"successInfo\": \"login-module-scss-module__4gE15G__successInfo\",\n  \"successLabel\": \"login-module-scss-module__4gE15G__successLabel\",\n  \"successSubtitle\": \"login-module-scss-module__4gE15G__successSubtitle\",\n  \"successTitle\": \"login-module-scss-module__4gE15G__successTitle\",\n  \"title\": \"login-module-scss-module__4gE15G__title\",\n  \"userDetails\": \"login-module-scss-module__4gE15G__userDetails\",\n  \"userInfo\": \"login-module-scss-module__4gE15G__userInfo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/components/LoginPresenter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { LoginPresenterProps } from '../types/login.types';\nimport styles from '../styles/login.module.scss';\n\nexport const LoginPresenter: React.FC<LoginPresenterProps> = ({\n  user,\n  isAuthenticated,\n  isLoading,\n  error,\n  formData,\n  onFormDataChange,\n  onSubmit,\n  onClearError,\n  onLogout,\n}) => {\n  // Handle input changes\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    onFormDataChange({\n      [name]: type === 'checkbox' ? checked : value,\n    });\n  };\n\n  // Handle form submission\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n\n  // Clear error when user starts typing\n  const handleInputFocus = () => {\n    if (error) {\n      onClearError();\n    }\n  };\n\n  // Show success state if authenticated\n  if (isAuthenticated && user) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.successCard}>\n          <div className={styles.header}>\n            <h2 className={styles.successTitle}>Welcome Back!</h2>\n            <p className={styles.successSubtitle}>You have successfully signed in.</p>\n          </div>\n\n          <div className={styles.successInfo}>\n            <div className={styles.successIcon}>\n              <svg className={styles.icon} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className={styles.userInfo}>\n              <div className={styles.successLabel}>Login Successful</div>\n              <div className={styles.userDetails}>\n                <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>\n                <p><strong>Email:</strong> {user.email}</p>\n                <p><strong>Role:</strong> {user.role}</p>\n                {user.phone && <p><strong>Phone:</strong> {user.phone}</p>}\n              </div>\n            </div>\n          </div>\n\n          <button\n            onClick={onLogout}\n            className={styles.logoutButton}\n          >\n            Sign Out\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Show login form\n  return (\n    <div className={styles.container}>\n      <div className={styles.loginCard}>\n        <div className={styles.header}>\n          <h2 className={styles.title}>Sign In</h2>\n          <p className={styles.subtitle}>Welcome back! Please sign in to your account.</p>\n        </div>\n\n        {error && (\n          <div className={styles.errorAlert}>\n            <div className={styles.errorContent}>\n              <div className={styles.errorIcon}>\n                <svg className={styles.icon} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className={styles.errorText}>\n                <div className={styles.errorTitle}>Error</div>\n                <p className={styles.errorMessage}>{error.message}</p>\n              </div>\n              <button\n                type=\"button\"\n                onClick={onClearError}\n                className={styles.errorClose}\n              >\n                <svg className={styles.closeIcon} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className={styles.form}>\n          <div className={styles.formGroup}>\n            <label htmlFor=\"email\" className={styles.label}>\n              Email\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              onFocus={handleInputFocus}\n              placeholder=\"Enter your email\"\n              required\n              disabled={isLoading}\n              className={styles.input}\n            />\n          </div>\n\n          <div className={styles.formGroup}>\n            <label htmlFor=\"password\" className={styles.label}>\n              Password\n            </label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              onFocus={handleInputFocus}\n              placeholder=\"Enter your password\"\n              required\n              disabled={isLoading}\n              className={styles.input}\n            />\n          </div>\n\n          <div className={styles.checkboxGroup}>\n            <input\n              type=\"checkbox\"\n              id=\"rememberMe\"\n              name=\"rememberMe\"\n              checked={formData.rememberMe}\n              onChange={handleInputChange}\n              className={styles.checkbox}\n            />\n            <label htmlFor=\"rememberMe\" className={styles.checkboxLabel}>\n              Remember me\n            </label>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className={styles.submitButton}\n          >\n            {isLoading && (\n              <div className={styles.spinner}></div>\n            )}\n            {isLoading ? 'Signing in...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className={styles.footer}>\n          <p className={styles.demoNote}>\n            Demo credentials: any email/password will work\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAMO,MAAM,iBAAgD,CAAC,EAC5D,IAAI,EACJ,eAAe,EACf,SAAS,EACT,KAAK,EACL,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,QAAQ,EACT;IACC,uBAAuB;IACvB,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,iBAAiB;YACf,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,SAAS;IACX;IAEA,sCAAsC;IACtC,MAAM,mBAAmB;QACvB,IAAI,OAAO;YACT;QACF;IACF;IAEA,sCAAsC;IACtC,IAAI,mBAAmB,MAAM;QAC3B,qBACE,8OAAC;YAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,8OAAC;gBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,WAAW;;kCAChC,8OAAC;wBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;0CAAE;;;;;;0CACpC,8OAAC;gCAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;kCAGxC,8OAAC;wBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;gCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,WAAW;0CAChC,cAAA,8OAAC;oCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,IAAI;oCAAE,MAAK;oCAAe,SAAQ;8CACvD,cAAA,8OAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAwI,UAAS;;;;;;;;;;;;;;;;0CAGhL,8OAAC;gCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;wCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;kDACrC,8OAAC;wCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,KAAK,SAAS;oDAAC;oDAAE,KAAK,QAAQ;;;;;;;0DACzD,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,KAAK,KAAK;;;;;;;0DACtC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,KAAK,IAAI;;;;;;;4CACnC,KAAK,KAAK,kBAAI,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;kCAK3D,8OAAC;wBACC,SAAS;wBACT,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;kCAC/B;;;;;;;;;;;;;;;;;IAMT;IAEA,kBAAkB;IAClB,qBACE,8OAAC;QAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;kBAC9B,cAAA,8OAAC;YAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAG,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;sCAC7B,8OAAC;4BAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,QAAQ;sCAAE;;;;;;;;;;;;gBAGhC,uBACC,8OAAC;oBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,UAAU;8BAC/B,cAAA,8OAAC;wBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,8OAAC;gCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;0CAC9B,cAAA,8OAAC;oCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,IAAI;oCAAE,MAAK;oCAAe,SAAQ;8CACvD,cAAA,8OAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA0N,UAAS;;;;;;;;;;;;;;;;0CAGlQ,8OAAC;gCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;wCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,UAAU;kDAAE;;;;;;kDACnC,8OAAC;wCAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;kDAAG,MAAM,OAAO;;;;;;;;;;;;0CAEnD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAW,iLAAA,CAAA,UAAM,CAAC,UAAU;0CAE5B,cAAA,8OAAC;oCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;oCAAE,MAAK;oCAAe,SAAQ;8CAC5D,cAAA,8OAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOnP,8OAAC;oBAAK,UAAU;oBAAc,WAAW,iLAAA,CAAA,UAAM,CAAC,IAAI;;sCAClD,8OAAC;4BAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAM,SAAQ;oCAAQ,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,SAAS;oCACT,aAAY;oCACZ,QAAQ;oCACR,UAAU;oCACV,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;;;;;;;;;;;;sCAI3B,8OAAC;4BAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAM,SAAQ;oCAAW,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;8CAAE;;;;;;8CAGnD,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,SAAS;oCACT,aAAY;oCACZ,QAAQ;oCACR,UAAU;oCACV,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;;;;;;;;;;;;sCAI3B,8OAAC;4BAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,aAAa;;8CAClC,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,UAAU;oCAC5B,UAAU;oCACV,WAAW,iLAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;8CAE5B,8OAAC;oCAAM,SAAQ;oCAAa,WAAW,iLAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;;;;;;;sCAK/D,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;gCAE7B,2BACC,8OAAC;oCAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,OAAO;;;;;;gCAE/B,YAAY,kBAAkB;;;;;;;;;;;;;8BAInC,8OAAC;oBAAI,WAAW,iLAAA,CAAA,UAAM,CAAC,MAAM;8BAC3B,cAAA,8OAAC;wBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/hooks/redux.ts"], "sourcesContent": ["import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';\nimport type { RootState, AppDispatch } from '@/store';\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/redux/loginSlice.ts"], "sourcesContent": ["/**\n * Login Redux Slice\n * Self-contained Redux logic for login feature\n */\n\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { LoginState, LoginRequest, LoginResponse, ApiError } from '../types/login.types';\n\n// Initial state\nconst initialState: LoginState = {\n  // User data\n  user: null,\n  isAuthenticated: false,\n  \n  // Tokens\n  accessToken: null,\n  refreshToken: null,\n  tokenExpiresAt: null,\n  \n  // Loading states\n  isLoading: false,\n  \n  // Error states\n  error: null,\n  \n  // UI states\n  lastLoginAt: null,\n  rememberMe: false,\n};\n\n// Login slice\nconst loginSlice = createSlice({\n  name: 'login',\n  initialState,\n  reducers: {\n    // Login Actions\n    loginRequest: (state, action: PayloadAction<LoginRequest>) => {\n      state.isLoading = true;\n      state.error = null;\n      state.rememberMe = action.payload.rememberMe || false;\n    },\n    \n    loginSuccess: (state, action: PayloadAction<LoginResponse>) => {\n      state.isLoading = false;\n      state.user = action.payload.user;\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      state.isAuthenticated = true;\n      state.error = null;\n      state.lastLoginAt = new Date().toISOString();\n      \n      // Calculate token expiration\n      const expiresAt = new Date();\n      expiresAt.setSeconds(expiresAt.getSeconds() + action.payload.expiresIn);\n      state.tokenExpiresAt = expiresAt.toISOString();\n    },\n    \n    loginFailure: (state, action: PayloadAction<ApiError>) => {\n      state.isLoading = false;\n      state.error = action.payload;\n      state.user = null;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.isAuthenticated = false;\n    },\n    \n    // Logout Actions\n    logoutRequest: (state) => {\n      state.isLoading = true;\n    },\n    \n    logoutSuccess: (state) => {\n      state.isLoading = false;\n      state.user = null;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiresAt = null;\n      state.isAuthenticated = false;\n      state.error = null;\n      state.lastLoginAt = null;\n      state.rememberMe = false;\n    },\n    \n    // Utility Actions\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    \n    clearError: (state) => {\n      state.error = null;\n    },\n    \n    clearState: (state) => {\n      Object.assign(state, initialState);\n    },\n    \n    // Token Management\n    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string; expiresIn: number }>) => {\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      \n      // Calculate token expiration\n      const expiresAt = new Date();\n      expiresAt.setSeconds(expiresAt.getSeconds() + action.payload.expiresIn);\n      state.tokenExpiresAt = expiresAt.toISOString();\n    },\n    \n    clearTokens: (state) => {\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiresAt = null;\n      state.isAuthenticated = false;\n    },\n  },\n});\n\n// Export actions\nexport const {\n  loginRequest,\n  loginSuccess,\n  loginFailure,\n  logoutRequest,\n  logoutSuccess,\n  setLoading,\n  clearError,\n  clearState,\n  setTokens,\n  clearTokens,\n} = loginSlice.actions;\n\n// Export reducer\nexport const loginReducer = loginSlice.reducer;\n\n// Action creators for saga\nexport const loginActionCreators = {\n  login: {\n    request: loginRequest,\n    success: loginSuccess,\n    failure: loginFailure,\n  },\n  logout: {\n    request: logoutRequest,\n    success: logoutSuccess,\n    failure: loginFailure, // Reuse login failure for logout errors\n  },\n};\n\n// Selectors\nexport const selectLoginState = (state: { login: LoginState }) => state.login;\nexport const selectUser = (state: { login: LoginState }) => state.login.user;\nexport const selectIsAuthenticated = (state: { login: LoginState }) => state.login.isAuthenticated;\nexport const selectIsLoading = (state: { login: LoginState }) => state.login.isLoading;\nexport const selectError = (state: { login: LoginState }) => state.login.error;\nexport const selectAccessToken = (state: { login: LoginState }) => state.login.accessToken;\nexport const selectRefreshToken = (state: { login: LoginState }) => state.login.refreshToken;\n\n// Export slice\nexport default loginSlice;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;AAED;;AAGA,gBAAgB;AAChB,MAAM,eAA2B;IAC/B,YAAY;IACZ,MAAM;IACN,iBAAiB;IAEjB,SAAS;IACT,aAAa;IACb,cAAc;IACd,gBAAgB;IAEhB,iBAAiB;IACjB,WAAW;IAEX,eAAe;IACf,OAAO;IAEP,YAAY;IACZ,aAAa;IACb,YAAY;AACd;AAEA,cAAc;AACd,MAAM,aAAa,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,gBAAgB;QAChB,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;YACd,MAAM,UAAU,GAAG,OAAO,OAAO,CAAC,UAAU,IAAI;QAClD;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9C,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY;YAChD,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,WAAW,GAAG,IAAI,OAAO,WAAW;YAE1C,6BAA6B;YAC7B,MAAM,YAAY,IAAI;YACtB,UAAU,UAAU,CAAC,UAAU,UAAU,KAAK,OAAO,OAAO,CAAC,SAAS;YACtE,MAAM,cAAc,GAAG,UAAU,WAAW;QAC9C;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,IAAI,GAAG;YACb,MAAM,WAAW,GAAG;YACpB,MAAM,YAAY,GAAG;YACrB,MAAM,eAAe,GAAG;QAC1B;QAEA,iBAAiB;QACjB,eAAe,CAAC;YACd,MAAM,SAAS,GAAG;QACpB;QAEA,eAAe,CAAC;YACd,MAAM,SAAS,GAAG;YAClB,MAAM,IAAI,GAAG;YACb,MAAM,WAAW,GAAG;YACpB,MAAM,YAAY,GAAG;YACrB,MAAM,cAAc,GAAG;YACvB,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,WAAW,GAAG;YACpB,MAAM,UAAU,GAAG;QACrB;QAEA,kBAAkB;QAClB,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QAEA,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QAEA,YAAY,CAAC;YACX,OAAO,MAAM,CAAC,OAAO;QACvB;QAEA,mBAAmB;QACnB,WAAW,CAAC,OAAO;YACjB,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,WAAW;YAC9C,MAAM,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY;YAEhD,6BAA6B;YAC7B,MAAM,YAAY,IAAI;YACtB,UAAU,UAAU,CAAC,UAAU,UAAU,KAAK,OAAO,OAAO,CAAC,SAAS;YACtE,MAAM,cAAc,GAAG,UAAU,WAAW;QAC9C;QAEA,aAAa,CAAC;YACZ,MAAM,WAAW,GAAG;YACpB,MAAM,YAAY,GAAG;YACrB,MAAM,cAAc,GAAG;YACvB,MAAM,eAAe,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,UAAU,EACV,UAAU,EACV,UAAU,EACV,SAAS,EACT,WAAW,EACZ,GAAG,WAAW,OAAO;AAGf,MAAM,eAAe,WAAW,OAAO;AAGvC,MAAM,sBAAsB;IACjC,OAAO;QACL,SAAS;QACT,SAAS;QACT,SAAS;IACX;IACA,QAAQ;QACN,SAAS;QACT,SAAS;QACT,SAAS;IACX;AACF;AAGO,MAAM,mBAAmB,CAAC,QAAiC,MAAM,KAAK;AACtE,MAAM,aAAa,CAAC,QAAiC,MAAM,KAAK,CAAC,IAAI;AACrE,MAAM,wBAAwB,CAAC,QAAiC,MAAM,KAAK,CAAC,eAAe;AAC3F,MAAM,kBAAkB,CAAC,QAAiC,MAAM,KAAK,CAAC,SAAS;AAC/E,MAAM,cAAc,CAAC,QAAiC,MAAM,KAAK,CAAC,KAAK;AACvE,MAAM,oBAAoB,CAAC,QAAiC,MAAM,KAAK,CAAC,WAAW;AACnF,MAAM,qBAAqB,CAAC,QAAiC,MAAM,KAAK,CAAC,YAAY;uCAG7E", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/states/LoginCallState.tsx"], "sourcesContent": ["/**\n * Login Call State\n * State management component for login feature\n */\n\n'use client';\n\nimport React from 'react';\nimport { useAppDispatch, useAppSelector } from '@/hooks/redux';\nimport { \n  loginRequest, \n  logoutRequest, \n  clearError,\n  selectLoginState \n} from '../redux/loginSlice';\nimport { LoginCallStateProps, LoginRequest } from '../types/login.types';\n\nexport const LoginCallState: React.FC<LoginCallStateProps> = ({ children }) => {\n  const dispatch = useAppDispatch();\n  const loginState = useAppSelector(selectLoginState);\n\n  // Login action\n  const login = (credentials: LoginRequest) => {\n    dispatch(loginRequest(credentials));\n  };\n\n  // Logout action\n  const logout = () => {\n    dispatch(logoutRequest());\n  };\n\n  // Clear error action\n  const clearErrorAction = () => {\n    dispatch(clearError());\n  };\n\n  return (\n    <>\n      {children({\n        user: loginState.user,\n        isAuthenticated: loginState.isAuthenticated,\n        isLoading: loginState.isLoading,\n        error: loginState.error,\n        login,\n        logout,\n        clearError: clearErrorAction,\n      })}\n    </>\n  );\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AACA;AAJA;;;;AAYO,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE;IACxE,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,uJAAA,CAAA,mBAAgB;IAElD,eAAe;IACf,MAAM,QAAQ,CAAC;QACb,SAAS,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE;IACxB;IAEA,gBAAgB;IAChB,MAAM,SAAS;QACb,SAAS,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD;IACvB;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,SAAS,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD;IACpB;IAEA,qBACE;kBACG,SAAS;YACR,MAAM,WAAW,IAAI;YACrB,iBAAiB,WAAW,eAAe;YAC3C,WAAW,WAAW,SAAS;YAC/B,OAAO,WAAW,KAAK;YACvB;YACA;YACA,YAAY;QACd;;AAGN", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/containers/LoginContainer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { LoginPresenter } from '../components/LoginPresenter';\nimport { LoginCallState } from '../states/LoginCallState';\nimport { LoginContainerProps, LoginFormData, User, ApiError } from '../types/login.types';\n\nexport const LoginContainer: React.FC<LoginContainerProps> = ({\n  onLoginSuccess,\n  onLoginError,\n  redirectTo = '/',\n}) => {\n  const router = useRouter();\n  \n  // Form state\n  const [formData, setFormData] = useState<LoginFormData>({\n    email: '',\n    password: '',\n    rememberMe: false,\n  });\n\n  // Form data change handler\n  const handleFormDataChange = useCallback((data: Partial<LoginFormData>) => {\n    setFormData(prev => ({ ...prev, ...data }));\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((user: User) => {\n    // Call external success handler if provided\n    if (onLoginSuccess) {\n      onLoginSuccess(user);\n    }\n    \n    // Redirect to specified route\n    router.push(redirectTo);\n  }, [onLoginSuccess, redirectTo, router]);\n\n  // Login error handler\n  const handleLoginError = useCallback((error: ApiError) => {\n    // Call external error handler if provided\n    if (onLoginError) {\n      onLoginError(error);\n    }\n    \n    // You could also show a toast notification here\n    console.error('Login error:', error);\n  }, [onLoginError]);\n\n  // Auto-redirect if already authenticated\n  const handleAuthCheck = useCallback((isAuthenticated: boolean, user: User | null) => {\n    if (isAuthenticated && user) {\n      router.push(redirectTo);\n    }\n  }, [redirectTo, router]);\n\n  return (\n    <LoginCallState>\n      {({ user, isAuthenticated, isLoading, error, login, logout, clearError }) => {\n        // Auto-redirect check\n        useEffect(() => {\n          handleAuthCheck(isAuthenticated, user);\n        }, [isAuthenticated, user, handleAuthCheck]);\n\n        // Handle form submission\n        const handleSubmit = useCallback((formData: LoginFormData) => {\n          login({\n            email: formData.email,\n            password: formData.password,\n            rememberMe: formData.rememberMe,\n          });\n        }, [login]);\n\n        // Handle login success\n        useEffect(() => {\n          if (isAuthenticated && user && !isLoading) {\n            handleLoginSuccess(user);\n          }\n        }, [isAuthenticated, user, isLoading, handleLoginSuccess]);\n\n        // Handle login error\n        useEffect(() => {\n          if (error && !isLoading) {\n            handleLoginError(error);\n          }\n        }, [error, isLoading, handleLoginError]);\n\n        return (\n          <LoginPresenter\n            user={user}\n            isAuthenticated={isAuthenticated}\n            isLoading={isLoading}\n            error={error}\n            formData={formData}\n            onFormDataChange={handleFormDataChange}\n            onSubmit={handleSubmit}\n            onClearError={clearError}\n            onLogout={logout}\n          />\n        );\n      }}\n    </LoginCallState>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAQO,MAAM,iBAAgD,CAAC,EAC5D,cAAc,EACd,YAAY,EACZ,aAAa,GAAG,EACjB;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,OAAO;QACP,UAAU;QACV,YAAY;IACd;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,IAAI;YAAC,CAAC;IAC3C,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,4CAA4C;QAC5C,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,8BAA8B;QAC9B,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;QAAgB;QAAY;KAAO;IAEvC,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,0CAA0C;QAC1C,IAAI,cAAc;YAChB,aAAa;QACf;QAEA,gDAAgD;QAChD,QAAQ,KAAK,CAAC,gBAAgB;IAChC,GAAG;QAAC;KAAa;IAEjB,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,iBAA0B;QAC7D,IAAI,mBAAmB,MAAM;YAC3B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,qBACE,8OAAC,6JAAA,CAAA,iBAAc;kBACZ,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE;YACtE,sBAAsB;YACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;gBACR,gBAAgB,iBAAiB;YACnC,GAAG;gBAAC;gBAAiB;gBAAM;aAAgB;YAE3C,yBAAyB;YACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;gBAChC,MAAM;oBACJ,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;gBACjC;YACF,GAAG;gBAAC;aAAM;YAEV,uBAAuB;YACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;gBACR,IAAI,mBAAmB,QAAQ,CAAC,WAAW;oBACzC,mBAAmB;gBACrB;YACF,GAAG;gBAAC;gBAAiB;gBAAM;gBAAW;aAAmB;YAEzD,qBAAqB;YACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;gBACR,IAAI,SAAS,CAAC,WAAW;oBACvB,iBAAiB;gBACnB;YACF,GAAG;gBAAC;gBAAO;gBAAW;aAAiB;YAEvC,qBACE,8OAAC,iKAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,iBAAiB;gBACjB,WAAW;gBACX,OAAO;gBACP,UAAU;gBACV,kBAAkB;gBAClB,UAAU;gBACV,cAAc;gBACd,UAAU;;;;;;QAGhB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/redux/loginSaga.ts"], "sourcesContent": ["/**\n * Login Redux Saga\n * Self-contained saga logic for login feature\n */\n\nimport { all, fork, call, put, takeLatest } from 'redux-saga/effects';\nimport { PayloadAction } from '@reduxjs/toolkit';\nimport { \n  loginRequest, \n  loginSuccess, \n  loginFailure,\n  logoutRequest,\n  logoutSuccess,\n  setLoading \n} from './loginSlice';\nimport { LoginRequest, LoginResponse, ApiError } from '../types/login.types';\n\n// API Response interface\ninterface ApiResponse<T = any> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\n// Mock API calls (replace with actual API service)\nconst loginApi = {\n  login: async (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Mock successful login\n    return {\n      success: true,\n      data: {\n        user: {\n          id: '1',\n          email: credentials.email,\n          firstName: 'John',\n          lastName: 'Doe',\n          phone: '+1234567890',\n          avatar: null,\n          role: 'customer' as const,\n          isEmailVerified: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        },\n        accessToken: 'mock-access-token-' + Date.now(),\n        refreshToken: 'mock-refresh-token-' + Date.now(),\n        expiresIn: 3600,\n      },\n    };\n  },\n\n  logout: async (): Promise<ApiResponse<void>> => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return { success: true, data: undefined };\n  },\n};\n\n// Login saga with token storage\nfunction* handleLogin(action: PayloadAction<LoginRequest>) {\n  try {\n    yield put(setLoading(true));\n    \n    const response: ApiResponse<LoginResponse> = yield call(loginApi.login, action.payload);\n    \n    if (response.success) {\n      // Store tokens in localStorage\n      localStorage.setItem('accessToken', response.data.accessToken);\n      localStorage.setItem('refreshToken', response.data.refreshToken);\n      \n      yield put(loginSuccess(response.data));\n    } else {\n      yield put(loginFailure({\n        message: response.message || 'Login failed',\n        status: 401\n      }));\n    }\n  } catch (error: any) {\n    yield put(loginFailure({\n      message: error.message || 'Network error occurred',\n      status: error.status || 500,\n      code: error.code\n    }));\n  } finally {\n    yield put(setLoading(false));\n  }\n}\n\n// Logout saga with token clearing\nfunction* handleLogout() {\n  try {\n    yield call(loginApi.logout);\n    \n    // Clear tokens regardless of API response\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    \n    yield put(logoutSuccess());\n  } catch (error: any) {\n    // Clear tokens even on error\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    \n    // Still dispatch success since logout should always succeed locally\n    yield put(logoutSuccess());\n  }\n}\n\n// Watch functions\nfunction* watchLogin() {\n  yield takeLatest(loginRequest.type, handleLogin);\n}\n\nfunction* watchLogout() {\n  yield takeLatest(logoutRequest.type, handleLogout);\n}\n\n// Root login saga\nexport function* loginSaga() {\n  yield all([\n    fork(watchLogin),\n    fork(watchLogout),\n  ]);\n}\n\n// Export individual sagas for testing\nexport { handleLogin, handleLogout };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAiBA,mDAAmD;AACnD,MAAM,WAAW;IACf,OAAO,OAAO;QACZ,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,wBAAwB;QACxB,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,MAAM;oBACJ,IAAI;oBACJ,OAAO,YAAY,KAAK;oBACxB,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,iBAAiB;oBACjB,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,aAAa,uBAAuB,KAAK,GAAG;gBAC5C,cAAc,wBAAwB,KAAK,GAAG;gBAC9C,WAAW;YACb;QACF;IACF;IAEA,QAAQ;QACN,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,OAAO;YAAE,SAAS;YAAM,MAAM;QAAU;IAC1C;AACF;AAEA,gCAAgC;AAChC,UAAU,YAAY,MAAmC;IACvD,IAAI;QACF,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE;QAErB,MAAM,WAAuC,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,SAAS,KAAK,EAAE,OAAO,OAAO;QAEtF,IAAI,SAAS,OAAO,EAAE;YACpB,+BAA+B;YAC/B,aAAa,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,WAAW;YAC7D,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,YAAY;YAE/D,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE,SAAS,IAAI;QACtC,OAAO;YACL,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE;gBACrB,SAAS,SAAS,OAAO,IAAI;gBAC7B,QAAQ;YACV;QACF;IACF,EAAE,OAAO,OAAY;QACnB,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE;YACrB,SAAS,MAAM,OAAO,IAAI;YAC1B,QAAQ,MAAM,MAAM,IAAI;YACxB,MAAM,MAAM,IAAI;QAClB;IACF,SAAU;QACR,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE;IACvB;AACF;AAEA,kCAAkC;AAClC,UAAU;IACR,IAAI;QACF,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,SAAS,MAAM;QAE1B,0CAA0C;QAC1C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD;IACxB,EAAE,OAAO,OAAY;QACnB,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,oEAAoE;QACpE,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD;IACxB;AACF;AAEA,kBAAkB;AAClB,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,uJAAA,CAAA,eAAY,CAAC,IAAI,EAAE;AACtC;AAEA,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,uJAAA,CAAA,gBAAa,CAAC,IAAI,EAAE;AACvC;AAGO,UAAU;IACf,MAAM,CAAA,GAAA,8LAAA,CAAA,MAAG,AAAD,EAAE;QACR,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE;QACL,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE;KACN;AACH", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/auth/login/index.ts"], "sourcesContent": ["// Login Feature Barrel Export\n// Self-contained login module exports\n\n// Export containers\nexport { LoginContainer } from './containers/LoginContainer';\n\n// Export components\nexport { LoginPresenter } from './components/LoginPresenter';\n\n// Export states\nexport { LoginCallState } from './states/LoginCallState';\n\n// Export redux\nexport { \n  loginReducer,\n  loginRequest,\n  loginSuccess,\n  loginFailure,\n  logoutRequest,\n  logoutSuccess,\n  setLoading,\n  clearError,\n  clearState,\n  setTokens,\n  clearTokens,\n  loginActionCreators,\n  selectLoginState,\n  selectUser,\n  selectIsAuthenticated,\n  selectIsLoading,\n  selectError,\n  selectAccessToken,\n  selectRefreshToken,\n} from './redux/loginSlice';\n\nexport { loginSaga } from './redux/loginSaga';\n\n// Export types\nexport type {\n  User,\n  LoginRequest,\n  LoginResponse,\n  ApiError,\n  LoginState,\n  LoginFormData,\n  LoginContainerProps,\n  LoginPresenterProps,\n  LoginCallStateProps,\n} from './types/login.types';\n\n// Default export\nexport { LoginContainer as default } from './containers/LoginContainer';\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,sCAAsC;AAEtC,oBAAoB;;AACpB;AAEA,oBAAoB;AACpB;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf;AAsBA", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/Icons.tsx"], "sourcesContent": ["// Icons Component\n// Reusable icon components for home feature\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n}\n\n// Social Media Icons\nexport const TwitterIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n  </svg>\n);\n\nexport const FacebookIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n  </svg>\n);\n\nexport const InstagramIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n  </svg>\n);\n\nexport const PinterestIcon: React.FC<IconProps> = ({ className = \"w-4 h-4\" }) => (\n  <svg className={className} fill=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.342-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n  </svg>\n);\n\n// Payment Method Icons - Simple and Clean Design\nexport const VisaIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-blue-600 to-blue-800 rounded-md flex items-center justify-center shadow-sm`}>\n    <span className=\"text-white font-bold text-sm tracking-wider\">VISA</span>\n  </div>\n);\n\nexport const MastercardIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-red-500 to-orange-500 rounded-md flex items-center justify-center shadow-sm relative overflow-hidden`}>\n    <div className=\"absolute left-2 w-4 h-4 bg-red-600 rounded-full opacity-80\"></div>\n    <div className=\"absolute right-2 w-4 h-4 bg-orange-400 rounded-full opacity-80\"></div>\n    <span className=\"text-white font-bold text-xs z-10\">MC</span>\n  </div>\n);\n\nexport const PayPalIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-gradient-to-r from-blue-700 to-blue-500 rounded-md flex items-center justify-center shadow-sm`}>\n    <span className=\"text-white font-bold text-xs\">PayPal</span>\n  </div>\n);\n\nexport const ApplePayIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-black rounded-md flex items-center justify-center shadow-sm space-x-1`}>\n    <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n    </svg>\n    <span className=\"text-white font-medium text-xs\">Pay</span>\n  </div>\n);\n\nexport const GooglePayIcon: React.FC<IconProps> = ({ className = \"w-12 h-8\" }) => (\n  <div className={`${className} bg-white border border-gray-200 rounded-md flex items-center justify-center shadow-sm space-x-1`}>\n    <svg className=\"w-3 h-3\" viewBox=\"0 0 24 24\">\n      <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" fill=\"#4285F4\"/>\n      <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" fill=\"#34A853\"/>\n      <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\" fill=\"#FBBC05\"/>\n      <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" fill=\"#EA4335\"/>\n    </svg>\n    <span className=\"text-gray-700 font-medium text-xs\">Pay</span>\n  </div>\n);\n\n// Helper function to get social icon by platform\nexport const getSocialIcon = (platform: string, className?: string) => {\n  switch (platform) {\n    case 'Twitter':\n      return <TwitterIcon className={className} />;\n    case 'Facebook':\n      return <FacebookIcon className={className} />;\n    case 'Instagram':\n      return <InstagramIcon className={className} />;\n    case 'Pinterest':\n      return <PinterestIcon className={className} />;\n    default:\n      return <div className={className}>📱</div>;\n  }\n};\n\n// Helper function to get payment icon by ID\nexport const getPaymentIcon = (paymentId: string, className?: string) => {\n  const iconClassName = className || \"w-12 h-8\";\n\n  switch (paymentId) {\n    case 'visa':\n      return <VisaIcon className={iconClassName} />;\n    case 'mastercard':\n      return <MastercardIcon className={iconClassName} />;\n    case 'paypal':\n      return <PayPalIcon className={iconClassName} />;\n    case 'apple-pay':\n      return <ApplePayIcon className={iconClassName} />;\n    case 'google-pay':\n      return <GooglePayIcon className={iconClassName} />;\n    default:\n      return (\n        <div className={`${iconClassName} bg-gray-100 border border-gray-200 rounded-md flex items-center justify-center`}>\n          <span className=\"text-xs font-semibold text-gray-600\">\n            {paymentId.slice(0, 4).toUpperCase()}\n          </span>\n        </div>\n      );\n  }\n};\n"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,4CAA4C;;;;;;;;;;;;;;;;AASrC,MAAM,cAAmC,CAAC,EAAE,YAAY,SAAS,EAAE,iBACxE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,eAAoC,CAAC,EAAE,YAAY,SAAS,EAAE,iBACzE,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,gBAAqC,CAAC,EAAE,YAAY,SAAS,EAAE,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,gBAAqC,CAAC,EAAE,YAAY,SAAS,EAAE,iBAC1E,8OAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;kBACrD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAKL,MAAM,WAAgC,CAAC,EAAE,YAAY,UAAU,EAAE,iBACtE,8OAAC;QAAI,WAAW,GAAG,UAAU,iGAAiG,CAAC;kBAC7H,cAAA,8OAAC;YAAK,WAAU;sBAA8C;;;;;;;;;;;AAI3D,MAAM,iBAAsC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC5E,8OAAC;QAAI,WAAW,GAAG,UAAU,2HAA2H,CAAC;;0BACvJ,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAK,WAAU;0BAAoC;;;;;;;;;;;;AAIjD,MAAM,aAAkC,CAAC,EAAE,YAAY,UAAU,EAAE,iBACxE,8OAAC;QAAI,WAAW,GAAG,UAAU,iGAAiG,CAAC;kBAC7H,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAI5C,MAAM,eAAoC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC1E,8OAAC;QAAI,WAAW,GAAG,UAAU,yEAAyE,CAAC;;0BACrG,8OAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAe,SAAQ;0BAC9D,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;0BAEV,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAI9C,MAAM,gBAAqC,CAAC,EAAE,YAAY,UAAU,EAAE,iBAC3E,8OAAC;QAAI,WAAW,GAAG,UAAU,gGAAgG,CAAC;;0BAC5H,8OAAC;gBAAI,WAAU;gBAAU,SAAQ;;kCAC/B,8OAAC;wBAAK,GAAE;wBAA0H,MAAK;;;;;;kCACvI,8OAAC;wBAAK,GAAE;wBAAwI,MAAK;;;;;;kCACrJ,8OAAC;wBAAK,GAAE;wBAAgI,MAAK;;;;;;kCAC7I,8OAAC;wBAAK,GAAE;wBAAsI,MAAK;;;;;;;;;;;;0BAErJ,8OAAC;gBAAK,WAAU;0BAAoC;;;;;;;;;;;;AAKjD,MAAM,gBAAgB,CAAC,UAAkB;IAC9C,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;gBAAY,WAAW;;;;;;QACjC,KAAK;YACH,qBAAO,8OAAC;gBAAa,WAAW;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC;YACE,qBAAO,8OAAC;gBAAI,WAAW;0BAAW;;;;;;IACtC;AACF;AAGO,MAAM,iBAAiB,CAAC,WAAmB;IAChD,MAAM,gBAAgB,aAAa;IAEnC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;gBAAS,WAAW;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC;gBAAe,WAAW;;;;;;QACpC,KAAK;YACH,qBAAO,8OAAC;gBAAW,WAAW;;;;;;QAChC,KAAK;YACH,qBAAO,8OAAC;gBAAa,WAAW;;;;;;QAClC,KAAK;YACH,qBAAO,8OAAC;gBAAc,WAAW;;;;;;QACnC;YACE,qBACE,8OAAC;gBAAI,WAAW,GAAG,cAAc,+EAA+E,CAAC;0BAC/G,cAAA,8OAAC;oBAAK,WAAU;8BACb,UAAU,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;IAI5C;AACF", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomePresenter.tsx"], "sourcesContent": ["'use client';\n\n// Home Presenter Component\n// Presentational component for home page UI\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { HomePresenterProps } from '../types/home.types';\nimport { getSocialIcon, getPaymentIcon } from './Icons';\nimport { Banner } from './Banner';\nimport { ProductSection } from './ProductSection';\n\nexport const HomePresenter: React.FC<HomePresenterProps> = ({\n  navigation,\n  search,\n  footer,\n  banners,\n  newArrivals,\n  recommendedProducts,\n  productCategories,\n  isLoading,\n  error,\n  onNavigationItemClick,\n  onMenuToggle,\n  onSearchChange,\n  onSearchSubmit,\n  onSearchClear,\n  onClearError,\n  onProductClick,\n  onCategoryClick,\n  onBannerClick,\n}) => {\n  const [searchQuery, setSearchQuery] = useState(search.query);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    onSearchChange(value);\n  };\n\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      onSearchSubmit(searchQuery.trim());\n    }\n  };\n\n  const handleSearchClear = () => {\n    setSearchQuery('');\n    onSearchClear();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white flex flex-col\">\n      {/* Promotional Banner */}\n      <div className=\"bg-black text-white text-center py-2 px-4 relative\">\n        <p className=\"text-sm\">\n          Sign up and get 20% off to your first order.{' '}\n          <Link href=\"/auth/register\" className=\"underline font-medium hover:text-gray-200\">\n            Sign Up Now\n          </Link>\n        </p>\n        <button\n          className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 text-lg\"\n          aria-label=\"Close banner\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"text-3xl font-black text-black tracking-tight\">\n                FIT\n              </Link>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8 ml-12\">\n              {navigation.items.map((item) => (\n                <div key={item.id} className=\"relative group\">\n                  <button\n                    onClick={() => onNavigationItemClick(item.id)}\n                    className={`text-gray-900 hover:text-black px-1 py-2 text-base font-normal transition-colors flex items-center ${\n                      item.isActive ? 'text-black font-medium' : ''\n                    }`}\n                  >\n                    {item.label}\n                    {item.hasDropdown && (\n                      <svg className=\"ml-1 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                      </svg>\n                    )}\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {item.hasDropdown && item.dropdownItems && (\n                    <div className=\"absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.dropdownItems.map((dropdownItem) => (\n                          <Link\n                            key={dropdownItem.id}\n                            href={dropdownItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors\"\n                          >\n                            {dropdownItem.label}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </nav>\n\n            {/* Search Bar */}\n            <div className=\"flex-1 max-w-md mx-8\">\n              <form onSubmit={handleSearchSubmit} className=\"relative\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchQuery}\n                    onChange={handleSearchChange}\n                    placeholder=\"Search for products...\"\n                    className=\"w-full pl-12 pr-4 py-3 border-0 rounded-full focus:outline-none focus:ring-2 focus:ring-gray-200 bg-gray-100 text-gray-900 placeholder-gray-500\"\n                  />\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                    </svg>\n                  </div>\n                  {searchQuery && (\n                    <button\n                      type=\"button\"\n                      onClick={handleSearchClear}\n                      className=\"absolute inset-y-0 right-0 pr-4 flex items-center\"\n                    >\n                      <svg className=\"h-4 w-4 text-gray-400 hover:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                      </svg>\n                    </button>\n                  )}\n                </div>\n              </form>\n            </div>\n\n            {/* Right side icons */}\n            <div className=\"flex items-center space-x-6\">\n              <Link href=\"/cart\" className=\"text-gray-700 hover:text-black transition-colors\">\n                <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n              </Link>\n              <Link href=\"/profile\" className=\"text-gray-700 hover:text-black transition-colors\">\n                <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n              </Link>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden ml-4\">\n              <button\n                onClick={onMenuToggle}\n                className=\"text-gray-700 hover:text-black p-2\"\n              >\n                <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {navigation.isMenuOpen && (\n          <div className=\"md:hidden bg-white border-t border-gray-100\">\n            <div className=\"px-4 pt-2 pb-3 space-y-1\">\n              {navigation.items.map((item) => (\n                <Link\n                  key={item.id}\n                  href={item.href}\n                  className=\"block px-3 py-3 text-base font-medium text-gray-900 hover:text-black hover:bg-gray-50 rounded-md transition-colors\"\n                  onClick={() => onNavigationItemClick(item.id)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n\n            {/* Mobile Search */}\n            <div className=\"px-4 pb-4\">\n              <form onSubmit={handleSearchSubmit} className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={searchQuery}\n                  onChange={handleSearchChange}\n                  placeholder=\"Search for products...\"\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-gray-200 bg-gray-50\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 relative\">\n          <span className=\"block sm:inline\">{error}</span>\n          <button\n            onClick={onClearError}\n            className=\"absolute top-0 bottom-0 right-0 px-4 py-3\"\n          >\n            <span className=\"sr-only\">Dismiss</span>\n            ✕\n          </button>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {isLoading ? (\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-black\"></div>\n          </div>\n        ) : (\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Welcome to FIT</h1>\n              <p className=\"text-xl text-gray-600 mb-8\">\n                Discover fashion that fits your style\n              </p>\n              <div className=\"space-y-4 max-w-md mx-auto\">\n                <Link\n                  href=\"/shop\"\n                  className=\"block w-full bg-black text-white text-center py-3 px-6 rounded-lg hover:bg-gray-800 transition-colors font-medium\"\n                >\n                  Shop Now\n                </Link>\n                <Link\n                  href=\"/new-arrivals\"\n                  className=\"block w-full border border-black text-black text-center py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n                >\n                  New Arrivals\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-100 border-t border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"md:col-span-1\">\n              <h3 className=\"text-2xl font-bold text-black mb-4\">{footer.companyInfo.name}</h3>\n              <p className=\"text-gray-600 mb-6 text-sm leading-relaxed\">\n                {footer.companyInfo.description}\n              </p>\n              \n              {/* Social Links */}\n              <div className=\"flex space-x-4\">\n                {footer.socialLinks.map((social) => (\n                  <Link\n                    key={social.id}\n                    href={social.href}\n                    className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-black transition-colors\"\n                    aria-label={social.platform}\n                  >\n                    {getSocialIcon(social.platform, \"w-4 h-4\")}\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Footer Sections */}\n            {footer.sections.map((section) => (\n              <div key={section.id} className=\"md:col-span-1\">\n                <h4 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n                  {section.title}\n                </h4>\n                <ul className=\"space-y-3\">\n                  {section.links.map((link) => (\n                    <li key={link.id}>\n                      <Link\n                        href={link.href}\n                        className=\"text-gray-600 hover:text-gray-900 text-sm transition-colors\"\n                      >\n                        {link.label}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            ))}\n          </div>\n\n          {/* Bottom Footer */}\n          <div className=\"mt-12 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-600 text-sm mb-4 md:mb-0\">\n              {footer.companyInfo.copyright}\n            </p>\n            \n            {/* Payment Methods */}\n            <div className=\"flex flex-wrap gap-2\">\n              {footer.paymentMethods.map((payment) => (\n                <div key={payment.id} title={payment.name}>\n                  {getPaymentIcon(payment.id, \"w-12 h-8\")}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA,2BAA2B;AAC3B,4CAA4C;AAE5C;AACA;AAEA;AARA;;;;;AAYO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,MAAM,EACN,MAAM,EACN,OAAO,EACP,WAAW,EACX,mBAAmB,EACnB,iBAAiB,EACjB,SAAS,EACT,KAAK,EACL,qBAAqB,EACrB,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aAAa,EACd;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,KAAK;IAE3D,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,eAAe,YAAY,IAAI;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;4BAAU;4BACwB;0CAC7C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAiB,WAAU;0CAA4C;;;;;;;;;;;;kCAIpF,8OAAC;wBACC,WAAU;wBACV,cAAW;kCACZ;;;;;;;;;;;;0BAMH,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgD;;;;;;;;;;;8CAM3E,8OAAC;oCAAI,WAAU;8CACZ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDACC,SAAS,IAAM,sBAAsB,KAAK,EAAE;oDAC5C,WAAW,CAAC,mGAAmG,EAC7G,KAAK,QAAQ,GAAG,2BAA2B,IAC3C;;wDAED,KAAK,KAAK;wDACV,KAAK,WAAW,kBACf,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;gDAM1E,KAAK,WAAW,IAAI,KAAK,aAAa,kBACrC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC,6BACvB,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,aAAa,IAAI;gEACvB,WAAU;0EAET,aAAa,KAAK;+DAJd,aAAa,EAAE;;;;;;;;;;;;;;;;2CArBtB,KAAK,EAAE;;;;;;;;;;8CAoCrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,UAAU;wCAAoB,WAAU;kDAC5C,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU;oDACV,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;gDAGxE,6BACC,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAA4C,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACnG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASjF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAG3E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAC9B,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAM7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ9E,WAAW,UAAU,kBACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,sBAAsB,KAAK,EAAE;kDAE3C,KAAK,KAAK;uCALN,KAAK,EAAE;;;;;;;;;;0CAWlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAoB,WAAU;;sDAC5C,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU;4CACV,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAmB;;;;;;kCACnC,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAU;;;;;;4BAAc;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAK,WAAU;0BACb,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;6EAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC,OAAO,WAAW,CAAC,IAAI;;;;;;sDAC3E,8OAAC;4CAAE,WAAU;sDACV,OAAO,WAAW,CAAC,WAAW;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,OAAO,IAAI;oDACjB,WAAU;oDACV,cAAY,OAAO,QAAQ;8DAE1B,CAAA,GAAA,+IAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,QAAQ,EAAE;mDAL3B,OAAO,EAAE;;;;;;;;;;;;;;;;gCAYrB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACpB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;sEAET,KAAK,KAAK;;;;;;uDALN,KAAK,EAAE;;;;;;;;;;;uCANZ,QAAQ,EAAE;;;;;;;;;;;sCAqBxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,OAAO,WAAW,CAAC,SAAS;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;8CACZ,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC,wBAC1B,8OAAC;4CAAqB,OAAO,QAAQ,IAAI;sDACtC,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,EAAE,EAAE;2CADpB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC", "debugId": null}}, {"offset": {"line": 2107, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/mockData.ts"], "sourcesContent": ["import { Banner, Product, ProductCategory } from '../types/home.types';\n\n// Mock Banners\nexport const mockBanners: Banner[] = [\n  {\n    id: '1',\n    title: 'SUMMER CLEARANCE SALE',\n    subtitle: 'MLB',\n    description: 'SALE UP TO 50%',\n    image: '/images/banners/summer-sale.jpg',\n    link: '/sale',\n    buttonText: 'Shop Now',\n    type: 'hero',\n    isActive: true,\n  },\n  {\n    id: '2',\n    title: 'MLB SALE UP TO 50%',\n    subtitle: 'MUA 2 GIẢM 10%\\nMUA 3 GIẢM 15%',\n    description: '26.6 - 13.7',\n    image: '/images/banners/mlb-sale.jpg',\n    link: '/mlb-sale',\n    buttonText: 'Xem ngay',\n    type: 'promotion',\n    isActive: true,\n  },\n];\n\n// Mock Product Categories\nexport const mockProductCategories: ProductCategory[] = [\n  { id: 'clothes', name: 'CLOTHES', slug: 'clothes', isActive: true },\n  { id: 'hat', name: '<PERSON><PERSON>', slug: 'hat', isActive: true },\n  { id: 'shoes', name: '<PERSON>OE<PERSON>', slug: 'shoes', isActive: true },\n  { id: 'bag', name: '<PERSON><PERSON>', slug: 'bag', isActive: true },\n];\n\n// Mock New Arrivals\nexport const mockNewArrivals: Product[] = [\n  {\n    id: '1',\n    name: 'Áo khoác bomber tổ phối oversize varsity',\n    brand: 'MLB',\n    price: 2890000,\n    image: '/images/products/bomber-jacket.jpg',\n    images: ['/images/products/bomber-jacket.jpg', '/images/products/bomber-jacket-2.jpg'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.5,\n    reviewCount: 128,\n  },\n  {\n    id: '2',\n    name: 'Áo thun unisex cổ tròn tay Faded Varsity',\n    brand: 'MLB',\n    price: 1590000,\n    image: '/images/products/unisex-tee.jpg',\n    images: ['/images/products/unisex-tee.jpg', '/images/products/unisex-tee-2.jpg'],\n    category: 'clothes',\n    colors: ['beige', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.3,\n    reviewCount: 89,\n  },\n  {\n    id: '3',\n    name: 'Áo thun unisex cổ tròn tay ngắn varsity',\n    brand: 'MLB',\n    price: 1390000,\n    image: '/images/products/varsity-tee.jpg',\n    images: ['/images/products/varsity-tee.jpg', '/images/products/varsity-tee-2.jpg'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.7,\n    reviewCount: 156,\n  },\n  {\n    id: '4',\n    name: 'Áo sát nách nữ cổ tròn New York Yankees',\n    brand: 'MLB',\n    price: 1190000,\n    image: '/images/products/tank-top.jpg',\n    images: ['/images/products/tank-top.jpg', '/images/products/tank-top-2.jpg'],\n    category: 'clothes',\n    colors: ['black', 'white'],\n    sizes: ['S', 'M', 'L', 'XL'],\n    isNew: true,\n    rating: 4.4,\n    reviewCount: 73,\n  },\n];\n\n// Mock Recommended Products\nexport const mockRecommendedProducts: Product[] = [\n  {\n    id: '5',\n    name: 'Nón Snapback phối unisex Kings New York Yankees',\n    brand: 'MLB',\n    price: 1290000,\n    image: '/images/products/snapback-cap.jpg',\n    images: ['/images/products/snapback-cap.jpg', '/images/products/snapback-cap-2.jpg'],\n    category: 'hat',\n    colors: ['green', 'black'],\n    sizes: ['One Size'],\n    rating: 4.6,\n    reviewCount: 234,\n  },\n  {\n    id: '6',\n    name: 'Giày Sneakers unisex cổ thấp Chunky Runner Classic Monogram',\n    brand: 'MLB',\n    price: 3290000,\n    image: '/images/products/chunky-sneakers.jpg',\n    images: ['/images/products/chunky-sneakers.jpg', '/images/products/chunky-sneakers-2.jpg'],\n    category: 'shoes',\n    colors: ['white', 'beige'],\n    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],\n    rating: 4.8,\n    reviewCount: 312,\n  },\n  {\n    id: '7',\n    name: 'Nón bucket unisex Color Denim Unstructured',\n    brand: 'MLB',\n    price: 990000,\n    image: '/images/products/bucket-hat.jpg',\n    images: ['/images/products/bucket-hat.jpg', '/images/products/bucket-hat-2.jpg'],\n    category: 'hat',\n    colors: ['blue', 'black'],\n    sizes: ['One Size'],\n    rating: 4.2,\n    reviewCount: 98,\n  },\n  {\n    id: '8',\n    name: 'Túi tote chữ kiểu unisex Mega Bear unstructured',\n    brand: 'MLB',\n    price: 1890000,\n    image: '/images/products/tote-bag.jpg',\n    images: ['/images/products/tote-bag.jpg', '/images/products/tote-bag-2.jpg'],\n    category: 'bag',\n    colors: ['brown', 'black'],\n    sizes: ['One Size'],\n    rating: 4.5,\n    reviewCount: 167,\n  },\n  {\n    id: '9',\n    name: 'Dép quai ngang unisex New Monogram',\n    brand: 'MLB',\n    price: 1590000,\n    image: '/images/products/slides.jpg',\n    images: ['/images/products/slides.jpg', '/images/products/slides-2.jpg'],\n    category: 'shoes',\n    colors: ['beige', 'black'],\n    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],\n    rating: 4.3,\n    reviewCount: 145,\n  },\n];\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,MAAM;QACN,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;CACD;AAGM,MAAM,wBAA2C;IACtD;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM;QAAW,UAAU;IAAK;IAClE;QAAE,IAAI;QAAO,MAAM;QAAO,MAAM;QAAO,UAAU;IAAK;IACtD;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM;QAAS,UAAU;IAAK;IAC5D;QAAE,IAAI;QAAO,MAAM;QAAO,MAAM;QAAO,UAAU;IAAK;CACvD;AAGM,MAAM,kBAA6B;IACxC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAsC;SAAuC;QACtF,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAmC;SAAoC;QAChF,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAoC;SAAqC;QAClF,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAiC;SAAkC;QAC5E,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAK;YAAK;YAAK;SAAK;QAC5B,OAAO;QACP,QAAQ;QACR,aAAa;IACf;CACD;AAGM,MAAM,0BAAqC;IAChD;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAqC;SAAsC;QACpF,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAwC;SAAyC;QAC1F,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACvD,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAmC;SAAoC;QAChF,UAAU;QACV,QAAQ;YAAC;YAAQ;SAAQ;QACzB,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAAiC;SAAkC;QAC5E,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;SAAW;QACnB,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;YAAC;YAA+B;SAAgC;QACxE,UAAU;QACV,QAAQ;YAAC;YAAS;SAAQ;QAC1B,OAAO;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACvD,QAAQ;QACR,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/homeSlice.ts"], "sourcesContent": ["// Home Redux Slice\n// State management for home page functionality\n\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { RootState } from '@/store';\nimport {\n  HomeState,\n  NavigationItem,\n  FooterData,\n  ApiError,\n  SearchRequest,\n  Banner,\n  Product,\n  ProductCategory\n} from '../types/home.types';\nimport {\n  mockBanners,\n  mockNewArrivals,\n  mockRecommendedProducts,\n  mockProductCategories\n} from '../data/mockData';\n\n// Initial state\nconst initialState: HomeState = {\n  navigation: {\n    items: [\n      {\n        id: 'shop',\n        label: 'Shop',\n        href: '/shop',\n        isActive: false,\n        hasDropdown: true,\n        dropdownItems: [\n          { id: 'women', label: 'Women', href: '/shop/women' },\n          { id: 'men', label: 'Men', href: '/shop/men' },\n          { id: 'accessories', label: 'Accessories', href: '/shop/accessories' },\n        ]\n      },\n      {\n        id: 'on-sale',\n        label: 'On Sale',\n        href: '/sale',\n        isActive: false,\n      },\n      {\n        id: 'new-arrivals',\n        label: 'New Arrivals',\n        href: '/new-arrivals',\n        isActive: false,\n      },\n    ],\n    activeItem: null,\n    isMenuOpen: false,\n  },\n  search: {\n    query: '',\n    isSearching: false,\n    suggestions: [],\n    recentSearches: [],\n  },\n  footer: {\n    sections: [\n      {\n        id: 'help',\n        title: 'HELP',\n        links: [\n          { id: 'customer-support', label: 'Customer Support', href: '/help/support' },\n          { id: 'delivery-details', label: 'Delivery Details', href: '/help/delivery' },\n          { id: 'terms-conditions', label: 'Terms & Conditions', href: '/help/terms' },\n          { id: 'privacy-policy', label: 'Privacy Policy', href: '/help/privacy' },\n        ]\n      },\n      {\n        id: 'faq',\n        title: 'FAQ',\n        links: [\n          { id: 'account', label: 'Account', href: '/faq/account' },\n          { id: 'manage-deliveries', label: 'Manage Deliveries', href: '/faq/deliveries' },\n          { id: 'orders', label: 'Orders', href: '/faq/orders' },\n          { id: 'payments', label: 'Payments', href: '/faq/payments' },\n        ]\n      }\n    ],\n    socialLinks: [\n      { id: 'twitter', platform: 'Twitter', href: '#', icon: 'twitter' },\n      { id: 'facebook', platform: 'Facebook', href: '#', icon: 'facebook' },\n      { id: 'instagram', platform: 'Instagram', href: '#', icon: 'instagram' },\n      { id: 'pinterest', platform: 'Pinterest', href: '#', icon: 'pinterest' },\n    ],\n    paymentMethods: [\n      { id: 'visa', name: 'Visa', icon: 'visa' },\n      { id: 'mastercard', name: 'Mastercard', icon: 'mastercard' },\n      { id: 'paypal', name: 'PayPal', icon: 'paypal' },\n      { id: 'apple-pay', name: 'Apple Pay', icon: 'apple-pay' },\n      { id: 'google-pay', name: 'Google Pay', icon: 'google-pay' },\n    ],\n    companyInfo: {\n      name: 'FIT',\n      description: 'We have clothes that suits your style and which you\\'re proud to wear. From women to men.',\n      copyright: 'FIT © 2025, All Rights Reserved',\n    }\n  },\n  banners: mockBanners,\n  newArrivals: mockNewArrivals,\n  recommendedProducts: mockRecommendedProducts,\n  productCategories: mockProductCategories,\n  isLoading: false,\n  error: null,\n  isInitialized: false,\n};\n\n// Home slice\nconst homeSlice = createSlice({\n  name: 'home',\n  initialState,\n  reducers: {\n    // Navigation actions\n    setActiveNavigation: (state, action: PayloadAction<string>) => {\n      state.navigation.activeItem = action.payload;\n      state.navigation.items = state.navigation.items.map(item => ({\n        ...item,\n        isActive: item.id === action.payload\n      }));\n    },\n\n    toggleMenu: (state) => {\n      state.navigation.isMenuOpen = !state.navigation.isMenuOpen;\n    },\n\n    closeMenu: (state) => {\n      state.navigation.isMenuOpen = false;\n    },\n\n    // Search actions\n    updateSearch: (state, action: PayloadAction<string>) => {\n      state.search.query = action.payload;\n    },\n\n    setSearching: (state, action: PayloadAction<boolean>) => {\n      state.search.isSearching = action.payload;\n    },\n\n    submitSearch: (state, action: PayloadAction<SearchRequest>) => {\n      state.search.isSearching = true;\n      // Add to recent searches if not empty and not already present\n      if (action.payload.query && !state.search.recentSearches.includes(action.payload.query)) {\n        state.search.recentSearches.unshift(action.payload.query);\n        // Keep only last 5 searches\n        state.search.recentSearches = state.search.recentSearches.slice(0, 5);\n      }\n    },\n\n    submitSearchSuccess: (state, action: PayloadAction<string[]>) => {\n      state.search.isSearching = false;\n      state.search.suggestions = action.payload;\n    },\n\n    submitSearchFailure: (state, action: PayloadAction<ApiError>) => {\n      state.search.isSearching = false;\n      state.error = action.payload.message;\n    },\n\n    clearSearch: (state) => {\n      state.search.query = '';\n      state.search.suggestions = [];\n    },\n\n    // General actions\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n\n    clearError: (state) => {\n      state.error = null;\n    },\n\n    // Initialize home\n    initializeHome: (state) => {\n      state.isLoading = true;\n      state.error = null;\n    },\n\n    initializeHomeSuccess: (state, action: PayloadAction<{ navigation: NavigationItem[], footer: FooterData }>) => {\n      state.isLoading = false;\n      state.navigation.items = action.payload.navigation;\n      state.footer = action.payload.footer;\n      state.isInitialized = true;\n    },\n\n    initializeHomeFailure: (state, action: PayloadAction<ApiError>) => {\n      state.isLoading = false;\n      state.error = action.payload.message;\n      state.isInitialized = false;\n    },\n\n    // Product and Banner actions\n    selectProduct: (state, action: PayloadAction<string>) => {\n      // Handle product selection - could navigate to product detail page\n      console.log('Product selected:', action.payload);\n    },\n\n    selectCategory: (state, action: PayloadAction<string>) => {\n      // Handle category selection - could filter products or navigate to category page\n      console.log('Category selected:', action.payload);\n    },\n\n    selectBanner: (state, action: PayloadAction<string>) => {\n      // Handle banner click - could navigate to promotion page\n      console.log('Banner selected:', action.payload);\n    },\n\n    // Reset state\n    resetHomeState: () => initialState,\n  },\n});\n\n// Export actions\nexport const {\n  setActiveNavigation,\n  toggleMenu,\n  closeMenu,\n  updateSearch,\n  setSearching,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  clearSearch,\n  setLoading,\n  setError,\n  clearError,\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  selectProduct,\n  selectCategory,\n  selectBanner,\n  resetHomeState,\n} = homeSlice.actions;\n\n// Action creators object for easier import\nexport const homeActionCreators = homeSlice.actions;\n\n// Selectors\nexport const selectHomeState = (state: RootState) => state.home;\nexport const selectNavigation = (state: RootState) => state.home.navigation;\nexport const selectSearch = (state: RootState) => state.home.search;\nexport const selectFooter = (state: RootState) => state.home.footer;\nexport const selectBanners = (state: RootState) => state.home.banners;\nexport const selectNewArrivals = (state: RootState) => state.home.newArrivals;\nexport const selectRecommendedProducts = (state: RootState) => state.home.recommendedProducts;\nexport const selectProductCategories = (state: RootState) => state.home.productCategories;\nexport const selectIsLoading = (state: RootState) => state.home.isLoading;\nexport const selectError = (state: RootState) => state.home.error;\nexport const selectIsInitialized = (state: RootState) => state.home.isInitialized;\n\n// Export reducer\nexport const homeReducer = homeSlice.reducer;\nexport default homeSlice.reducer;\n"], "names": [], "mappings": "AAAA,mBAAmB;AACnB,+CAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C;AAYA;;;AAOA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,YAAY;QACV,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,eAAe;oBACb;wBAAE,IAAI;wBAAS,OAAO;wBAAS,MAAM;oBAAc;oBACnD;wBAAE,IAAI;wBAAO,OAAO;wBAAO,MAAM;oBAAY;oBAC7C;wBAAE,IAAI;wBAAe,OAAO;wBAAe,MAAM;oBAAoB;iBACtE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,YAAY;QACZ,YAAY;IACd;IACA,QAAQ;QACN,OAAO;QACP,aAAa;QACb,aAAa,EAAE;QACf,gBAAgB,EAAE;IACpB;IACA,QAAQ;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,OAAO;oBACL;wBAAE,IAAI;wBAAoB,OAAO;wBAAoB,MAAM;oBAAgB;oBAC3E;wBAAE,IAAI;wBAAoB,OAAO;wBAAoB,MAAM;oBAAiB;oBAC5E;wBAAE,IAAI;wBAAoB,OAAO;wBAAsB,MAAM;oBAAc;oBAC3E;wBAAE,IAAI;wBAAkB,OAAO;wBAAkB,MAAM;oBAAgB;iBACxE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,OAAO;oBACL;wBAAE,IAAI;wBAAW,OAAO;wBAAW,MAAM;oBAAe;oBACxD;wBAAE,IAAI;wBAAqB,OAAO;wBAAqB,MAAM;oBAAkB;oBAC/E;wBAAE,IAAI;wBAAU,OAAO;wBAAU,MAAM;oBAAc;oBACrD;wBAAE,IAAI;wBAAY,OAAO;wBAAY,MAAM;oBAAgB;iBAC5D;YACH;SACD;QACD,aAAa;YACX;gBAAE,IAAI;gBAAW,UAAU;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACjE;gBAAE,IAAI;gBAAY,UAAU;gBAAY,MAAM;gBAAK,MAAM;YAAW;YACpE;gBAAE,IAAI;gBAAa,UAAU;gBAAa,MAAM;gBAAK,MAAM;YAAY;YACvE;gBAAE,IAAI;gBAAa,UAAU;gBAAa,MAAM;gBAAK,MAAM;YAAY;SACxE;QACD,gBAAgB;YACd;gBAAE,IAAI;gBAAQ,MAAM;gBAAQ,MAAM;YAAO;YACzC;gBAAE,IAAI;gBAAc,MAAM;gBAAc,MAAM;YAAa;YAC3D;gBAAE,IAAI;gBAAU,MAAM;gBAAU,MAAM;YAAS;YAC/C;gBAAE,IAAI;gBAAa,MAAM;gBAAa,MAAM;YAAY;YACxD;gBAAE,IAAI;gBAAc,MAAM;gBAAc,MAAM;YAAa;SAC5D;QACD,aAAa;YACX,MAAM;YACN,aAAa;YACb,WAAW;QACb;IACF;IACA,SAAS,2IAAA,CAAA,cAAW;IACpB,aAAa,2IAAA,CAAA,kBAAe;IAC5B,qBAAqB,2IAAA,CAAA,0BAAuB;IAC5C,mBAAmB,2IAAA,CAAA,wBAAqB;IACxC,WAAW;IACX,OAAO;IACP,eAAe;AACjB;AAEA,aAAa;AACb,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,qBAAqB;QACrB,qBAAqB,CAAC,OAAO;YAC3B,MAAM,UAAU,CAAC,UAAU,GAAG,OAAO,OAAO;YAC5C,MAAM,UAAU,CAAC,KAAK,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3D,GAAG,IAAI;oBACP,UAAU,KAAK,EAAE,KAAK,OAAO,OAAO;gBACtC,CAAC;QACH;QAEA,YAAY,CAAC;YACX,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM,UAAU,CAAC,UAAU;QAC5D;QAEA,WAAW,CAAC;YACV,MAAM,UAAU,CAAC,UAAU,GAAG;QAChC;QAEA,iBAAiB;QACjB,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,OAAO;QACrC;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,WAAW,GAAG,OAAO,OAAO;QAC3C;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,8DAA8D;YAC9D,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC,KAAK,GAAG;gBACvF,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,OAAO,CAAC,KAAK;gBACxD,4BAA4B;gBAC5B,MAAM,MAAM,CAAC,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;YACrE;QACF;QAEA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG,OAAO,OAAO;QAC3C;QAEA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,MAAM,CAAC,WAAW,GAAG;YAC3B,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,OAAO;QACtC;QAEA,aAAa,CAAC;YACZ,MAAM,MAAM,CAAC,KAAK,GAAG;YACrB,MAAM,MAAM,CAAC,WAAW,GAAG,EAAE;QAC/B;QAEA,kBAAkB;QAClB,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QAEA,kBAAkB;QAClB,gBAAgB,CAAC;YACf,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG;QAChB;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,SAAS,GAAG;YAClB,MAAM,UAAU,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,UAAU;YAClD,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM;YACpC,MAAM,aAAa,GAAG;QACxB;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,SAAS,GAAG;YAClB,MAAM,KAAK,GAAG,OAAO,OAAO,CAAC,OAAO;YACpC,MAAM,aAAa,GAAG;QACxB;QAEA,6BAA6B;QAC7B,eAAe,CAAC,OAAO;YACrB,mEAAmE;YACnE,QAAQ,GAAG,CAAC,qBAAqB,OAAO,OAAO;QACjD;QAEA,gBAAgB,CAAC,OAAO;YACtB,iFAAiF;YACjF,QAAQ,GAAG,CAAC,sBAAsB,OAAO,OAAO;QAClD;QAEA,cAAc,CAAC,OAAO;YACpB,yDAAyD;YACzD,QAAQ,GAAG,CAAC,oBAAoB,OAAO,OAAO;QAChD;QAEA,cAAc;QACd,gBAAgB,IAAM;IACxB;AACF;AAGO,MAAM,EACX,mBAAmB,EACnB,UAAU,EACV,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,cAAc,EACd,qBAAqB,EACrB,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,cAAc,EACf,GAAG,UAAU,OAAO;AAGd,MAAM,qBAAqB,UAAU,OAAO;AAG5C,MAAM,kBAAkB,CAAC,QAAqB,MAAM,IAAI;AACxD,MAAM,mBAAmB,CAAC,QAAqB,MAAM,IAAI,CAAC,UAAU;AACpE,MAAM,eAAe,CAAC,QAAqB,MAAM,IAAI,CAAC,MAAM;AAC5D,MAAM,eAAe,CAAC,QAAqB,MAAM,IAAI,CAAC,MAAM;AAC5D,MAAM,gBAAgB,CAAC,QAAqB,MAAM,IAAI,CAAC,OAAO;AAC9D,MAAM,oBAAoB,CAAC,QAAqB,MAAM,IAAI,CAAC,WAAW;AACtE,MAAM,4BAA4B,CAAC,QAAqB,MAAM,IAAI,CAAC,mBAAmB;AACtF,MAAM,0BAA0B,CAAC,QAAqB,MAAM,IAAI,CAAC,iBAAiB;AAClF,MAAM,kBAAkB,CAAC,QAAqB,MAAM,IAAI,CAAC,SAAS;AAClE,MAAM,cAAc,CAAC,QAAqB,MAAM,IAAI,CAAC,KAAK;AAC1D,MAAM,sBAAsB,CAAC,QAAqB,MAAM,IAAI,CAAC,aAAa;AAG1E,MAAM,cAAc,UAAU,OAAO;uCAC7B,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeCallState.tsx"], "sourcesContent": ["'use client';\n\n// Home Call State Component\n// Connects Redux state to UI components for home feature\n\nimport React, { useEffect, useCallback } from 'react';\nimport { useAppDispatch, useAppSelector } from '@/hooks/redux';\nimport {\n  selectNavigation,\n  selectSearch,\n  selectFooter,\n  selectBanners,\n  selectNewArrivals,\n  selectRecommendedProducts,\n  selectProductCategories,\n  selectIsLoading,\n  selectError,\n  selectIsInitialized,\n  setActiveNavigation,\n  toggleMenu,\n  updateSearch,\n  submitSearch,\n  clearSearch,\n  clearError,\n  initializeHome,\n  selectProduct,\n  selectCategory,\n  selectBanner,\n} from '../redux/homeSlice';\nimport { HomeCallStateProps, SearchRequest } from '../types/home.types';\n\nexport const HomeCallState: React.FC<HomeCallStateProps> = ({ children }) => {\n  const dispatch = useAppDispatch();\n  \n  // Select state from Redux store\n  const navigation = useAppSelector(selectNavigation);\n  const search = useAppSelector(selectSearch);\n  const footer = useAppSelector(selectFooter);\n  const banners = useAppSelector(selectBanners);\n  const newArrivals = useAppSelector(selectNewArrivals);\n  const recommendedProducts = useAppSelector(selectRecommendedProducts);\n  const productCategories = useAppSelector(selectProductCategories);\n  const isLoading = useAppSelector(selectIsLoading);\n  const error = useAppSelector(selectError);\n  const isInitialized = useAppSelector(selectIsInitialized);\n\n  // Action handlers\n  const setActiveNavigation = useCallback((itemId: string) => {\n    dispatch(setActiveNavigation(itemId));\n  }, [dispatch]);\n\n  const toggleMenu = useCallback(() => {\n    dispatch(toggleMenu());\n  }, [dispatch]);\n\n  const updateSearchQuery = useCallback((query: string) => {\n    dispatch(updateSearch(query));\n  }, [dispatch]);\n\n  const submitSearchQuery = useCallback((query: string) => {\n    const searchRequest: SearchRequest = {\n      query: query.trim(),\n    };\n    dispatch(submitSearch(searchRequest));\n  }, [dispatch]);\n\n  const clearSearchQuery = useCallback(() => {\n    dispatch(clearSearch());\n  }, [dispatch]);\n\n  const clearErrorMessage = useCallback(() => {\n    dispatch(clearError());\n  }, [dispatch]);\n\n  const initializeHomePage = useCallback(() => {\n    dispatch(initializeHome());\n  }, [dispatch]);\n\n  const handleProductSelect = useCallback((productId: string) => {\n    dispatch(selectProduct(productId));\n  }, [dispatch]);\n\n  const handleCategorySelect = useCallback((categoryId: string) => {\n    dispatch(selectCategory(categoryId));\n  }, [dispatch]);\n\n  const handleBannerSelect = useCallback((bannerId: string) => {\n    dispatch(selectBanner(bannerId));\n  }, [dispatch]);\n\n  // Initialize home page on mount if not already initialized\n  useEffect(() => {\n    if (!isInitialized && !isLoading) {\n      initializeHomePage();\n    }\n  }, [isInitialized, isLoading, initializeHomePage]);\n\n  // Auto-clear error after 5 seconds\n  useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => {\n        clearErrorMessage();\n      }, 5000);\n\n      return () => clearTimeout(timer);\n    }\n  }, [error, clearErrorMessage]);\n\n  return (\n    <>\n      {children({\n        navigation,\n        search,\n        footer,\n        banners,\n        newArrivals,\n        recommendedProducts,\n        productCategories,\n        isLoading,\n        error,\n        setActiveNavigation,\n        toggleMenu,\n        updateSearch: updateSearchQuery,\n        submitSearch: submitSearchQuery,\n        clearSearch: clearSearchQuery,\n        clearError: clearErrorMessage,\n        initializeHome: initializeHomePage,\n        selectProduct: handleProductSelect,\n        selectCategory: handleCategorySelect,\n        selectBanner: handleBannerSelect,\n      })}\n    </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA,4BAA4B;AAC5B,yDAAyD;AAEzD;AACA;AACA;AAPA;;;;;AA+BO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAE9B,gCAAgC;IAChC,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,mBAAgB;IAClD,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,eAAY;IAC1C,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,eAAY;IAC1C,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,gBAAa;IAC5C,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,oBAAiB;IACpD,MAAM,sBAAsB,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,4BAAyB;IACpE,MAAM,oBAAoB,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,0BAAuB;IAChE,MAAM,YAAY,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,kBAAe;IAChD,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,cAAW;IACxC,MAAM,gBAAgB,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,6IAAA,CAAA,sBAAmB;IAExD,kBAAkB;IAClB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,oBAAoB;IAC/B,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,SAAS,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,gBAA+B;YACnC,OAAO,MAAM,IAAI;QACnB;QACA,SAAS,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,SAAS,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD;IACrB,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,SAAS,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;IACpB,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,SAAS,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD;IACxB,GAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE;IACzB,GAAG;QAAC;KAAS;IAEb,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,SAAS,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;IAC1B,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,SAAS,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;IACxB,GAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAChC;QACF;IACF,GAAG;QAAC;QAAe;QAAW;KAAmB;IAEjD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAO;KAAkB;IAE7B,qBACE;kBACG,SAAS;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;YACd,cAAc;YACd,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,cAAc;QAChB;;AAGN", "debugId": null}}, {"offset": {"line": 2843, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/HomeContainer.tsx"], "sourcesContent": ["'use client';\n\n// Home Container Component\n// Business logic container for home page\n\nimport React, { useCallback, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { HomePresenter } from '../components/HomePresenter';\nimport { HomeCallState } from '../states/HomeCallState';\nimport { HomeContainerProps } from '../types/home.types';\n\nexport const HomeContainer: React.FC<HomeContainerProps> = ({\n  className,\n  onNavigationChange,\n  onSearch,\n}) => {\n  const router = useRouter();\n\n  // Navigation handler\n  const handleNavigationItemClick = useCallback((itemId: string) => {\n    // Call external navigation change handler if provided\n    if (onNavigationChange) {\n      onNavigationChange(itemId);\n    }\n\n    // Handle navigation based on item ID\n    switch (itemId) {\n      case 'shop':\n        router.push('/shop');\n        break;\n      case 'on-sale':\n        router.push('/sale');\n        break;\n      case 'new-arrivals':\n        router.push('/new-arrivals');\n        break;\n      default:\n        console.log(`Navigation to ${itemId} not implemented yet`);\n    }\n  }, [onNavigationChange, router]);\n\n  // Search handlers\n  const handleSearchChange = useCallback((query: string) => {\n    // Call external search handler if provided\n    if (onSearch) {\n      onSearch(query);\n    }\n  }, [onSearch]);\n\n  const handleSearchSubmit = useCallback((query: string) => {\n    // Navigate to search results page\n    if (query.trim()) {\n      router.push(`/search?q=${encodeURIComponent(query.trim())}`);\n    }\n  }, [router]);\n\n  const handleSearchClear = useCallback(() => {\n    // Clear search and call external handler\n    if (onSearch) {\n      onSearch('');\n    }\n  }, [onSearch]);\n\n  return (\n    <div className={className}>\n      <HomeCallState>\n        {({\n          navigation,\n          search,\n          footer,\n          banners,\n          newArrivals,\n          recommendedProducts,\n          productCategories,\n          isLoading,\n          error,\n          setActiveNavigation,\n          toggleMenu,\n          updateSearch,\n          submitSearch,\n          clearSearch,\n          clearError,\n          initializeHome,\n          selectProduct,\n          selectCategory,\n          selectBanner\n        }) => {\n          // Handle menu toggle\n          const handleMenuToggle = useCallback(() => {\n            toggleMenu();\n          }, [toggleMenu]);\n\n          // Handle navigation item click with state update\n          const handleNavigationClick = useCallback((itemId: string) => {\n            setActiveNavigation(itemId);\n            handleNavigationItemClick(itemId);\n          }, [setActiveNavigation, handleNavigationItemClick]);\n\n          // Handle search change with state update\n          const handleSearchChangeWithState = useCallback((query: string) => {\n            updateSearch(query);\n            handleSearchChange(query);\n          }, [updateSearch, handleSearchChange]);\n\n          // Handle search submit with state update\n          const handleSearchSubmitWithState = useCallback((query: string) => {\n            submitSearch({ query });\n            handleSearchSubmit(query);\n          }, [submitSearch, handleSearchSubmit]);\n\n          // Handle search clear with state update\n          const handleSearchClearWithState = useCallback(() => {\n            clearSearch();\n            handleSearchClear();\n          }, [clearSearch, handleSearchClear]);\n\n          // Handle product click\n          const handleProductClick = useCallback((productId: string) => {\n            selectProduct(productId);\n            // Navigate to product detail page\n            router.push(`/product/${productId}`);\n          }, [selectProduct, router]);\n\n          // Handle category click\n          const handleCategoryClick = useCallback((categoryId: string) => {\n            selectCategory(categoryId);\n            // Navigate to category page\n            router.push(`/category/${categoryId}`);\n          }, [selectCategory, router]);\n\n          // Handle banner click\n          const handleBannerClick = useCallback((bannerId: string) => {\n            selectBanner(bannerId);\n            // Navigate based on banner link\n            const banner = banners.find(b => b.id === bannerId);\n            if (banner?.link) {\n              router.push(banner.link);\n            }\n          }, [selectBanner, banners, router]);\n\n          return (\n            <HomePresenter\n              navigation={navigation}\n              search={search}\n              footer={footer}\n              banners={banners}\n              newArrivals={newArrivals}\n              recommendedProducts={recommendedProducts}\n              productCategories={productCategories}\n              isLoading={isLoading}\n              error={error}\n              onNavigationItemClick={handleNavigationClick}\n              onMenuToggle={handleMenuToggle}\n              onSearchChange={handleSearchChangeWithState}\n              onSearchSubmit={handleSearchSubmitWithState}\n              onSearchClear={handleSearchClearWithState}\n              onProductClick={handleProductClick}\n              onCategoryClick={handleCategoryClick}\n              onBannerClick={handleBannerClick}\n              onClearError={clearError}\n            />\n          );\n        }}\n      </HomeCallState>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA,2BAA2B;AAC3B,yCAAyC;AAEzC;AACA;AACA;AACA;AARA;;;;;;AAWO,MAAM,gBAA8C,CAAC,EAC1D,SAAS,EACT,kBAAkB,EAClB,QAAQ,EACT;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,qBAAqB;IACrB,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,sDAAsD;QACtD,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;QAEA,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF;gBACE,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,oBAAoB,CAAC;QAC7D;IACF,GAAG;QAAC;QAAoB;KAAO;IAE/B,kBAAkB;IAClB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,2CAA2C;QAC3C,IAAI,UAAU;YACZ,SAAS;QACX;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,kCAAkC;QAClC,IAAI,MAAM,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,MAAM,IAAI,KAAK;QAC7D;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,yCAAyC;QACzC,IAAI,UAAU;YACZ,SAAS;QACX;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAW;kBACd,cAAA,8OAAC,mJAAA,CAAA,gBAAa;sBACX,CAAC,EACA,UAAU,EACV,MAAM,EACN,MAAM,EACN,OAAO,EACP,WAAW,EACX,mBAAmB,EACnB,iBAAiB,EACjB,SAAS,EACT,KAAK,EACL,mBAAmB,EACnB,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACV,cAAc,EACd,aAAa,EACb,cAAc,EACd,YAAY,EACb;gBACC,qBAAqB;gBACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;oBACnC;gBACF,GAAG;oBAAC;iBAAW;gBAEf,iDAAiD;gBACjD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBACzC,oBAAoB;oBACpB,0BAA0B;gBAC5B,GAAG;oBAAC;oBAAqB;iBAA0B;gBAEnD,yCAAyC;gBACzC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBAC/C,aAAa;oBACb,mBAAmB;gBACrB,GAAG;oBAAC;oBAAc;iBAAmB;gBAErC,yCAAyC;gBACzC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBAC/C,aAAa;wBAAE;oBAAM;oBACrB,mBAAmB;gBACrB,GAAG;oBAAC;oBAAc;iBAAmB;gBAErC,wCAAwC;gBACxC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;oBAC7C;oBACA;gBACF,GAAG;oBAAC;oBAAa;iBAAkB;gBAEnC,uBAAuB;gBACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBACtC,cAAc;oBACd,kCAAkC;oBAClC,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW;gBACrC,GAAG;oBAAC;oBAAe;iBAAO;gBAE1B,wBAAwB;gBACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBACvC,eAAe;oBACf,4BAA4B;oBAC5B,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,YAAY;gBACvC,GAAG;oBAAC;oBAAgB;iBAAO;gBAE3B,sBAAsB;gBACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;oBACrC,aAAa;oBACb,gCAAgC;oBAChC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC1C,IAAI,QAAQ,MAAM;wBAChB,OAAO,IAAI,CAAC,OAAO,IAAI;oBACzB;gBACF,GAAG;oBAAC;oBAAc;oBAAS;iBAAO;gBAElC,qBACE,8OAAC,uJAAA,CAAA,gBAAa;oBACZ,YAAY;oBACZ,QAAQ;oBACR,QAAQ;oBACR,SAAS;oBACT,aAAa;oBACb,qBAAqB;oBACrB,mBAAmB;oBACnB,WAAW;oBACX,OAAO;oBACP,uBAAuB;oBACvB,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB;oBAChB,eAAe;oBACf,gBAAgB;oBAChB,iBAAiB;oBACjB,eAAe;oBACf,cAAc;;;;;;YAGpB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>/homeSaga.ts"], "sourcesContent": ["// Home Redux Saga\n// Side effects management for home page functionality\n\nimport { call, put, takeEvery, takeLatest, delay, select } from 'redux-saga/effects';\nimport { PayloadAction } from '@reduxjs/toolkit';\nimport {\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  setSearching,\n} from './homeSlice';\nimport { \n  SearchRequest, \n  NavigationItem, \n  FooterData, \n  ApiError \n} from '../types/home.types';\n\n// Mock API functions (replace with real API calls)\nconst mockApiDelay = () => new Promise(resolve => setTimeout(resolve, 1000));\n\nconst mockInitializeHomeApi = async (): Promise<{ navigation: NavigationItem[], footer: FooterData }> => {\n  await mockApiDelay();\n  \n  // Return mock data - in real app this would come from API\n  return {\n    navigation: [\n      {\n        id: 'shop',\n        label: 'Shop',\n        href: '/shop',\n        isActive: false,\n        hasDropdown: true,\n        dropdownItems: [\n          { id: 'women', label: 'Women', href: '/shop/women' },\n          { id: 'men', label: 'Men', href: '/shop/men' },\n          { id: 'accessories', label: 'Accessories', href: '/shop/accessories' },\n        ]\n      },\n      {\n        id: 'on-sale',\n        label: 'On Sale',\n        href: '/sale',\n        isActive: false,\n      },\n      {\n        id: 'new-arrivals',\n        label: 'New Arrivals',\n        href: '/new-arrivals',\n        isActive: false,\n      },\n    ],\n    footer: {\n      sections: [\n        {\n          id: 'help',\n          title: 'HELP',\n          links: [\n            { id: 'customer-support', label: 'Customer Support', href: '/help/support' },\n            { id: 'delivery-details', label: 'Delivery Details', href: '/help/delivery' },\n            { id: 'terms-conditions', label: 'Terms & Conditions', href: '/help/terms' },\n            { id: 'privacy-policy', label: 'Privacy Policy', href: '/help/privacy' },\n          ]\n        },\n        {\n          id: 'faq',\n          title: 'FAQ',\n          links: [\n            { id: 'account', label: 'Account', href: '/faq/account' },\n            { id: 'manage-deliveries', label: 'Manage Deliveries', href: '/faq/deliveries' },\n            { id: 'orders', label: 'Orders', href: '/faq/orders' },\n            { id: 'payments', label: 'Payments', href: '/faq/payments' },\n          ]\n        }\n      ],\n      socialLinks: [\n        { id: 'twitter', platform: 'Twitter', href: '#', icon: 'twitter' },\n        { id: 'facebook', platform: 'Facebook', href: '#', icon: 'facebook' },\n        { id: 'instagram', platform: 'Instagram', href: '#', icon: 'instagram' },\n        { id: 'pinterest', platform: 'Pinterest', href: '#', icon: 'pinterest' },\n      ],\n      paymentMethods: [\n        { id: 'visa', name: 'Visa', icon: 'visa' },\n        { id: 'mastercard', name: 'Mastercard', icon: 'mastercard' },\n        { id: 'paypal', name: 'PayPal', icon: 'paypal' },\n        { id: 'apple-pay', name: 'Apple Pay', icon: 'apple-pay' },\n        { id: 'google-pay', name: 'Google Pay', icon: 'google-pay' },\n      ],\n      companyInfo: {\n        name: 'FIT',\n        description: 'We have clothes that suits your style and which you\\'re proud to wear. From women to men.',\n        copyright: 'FIT © 2025, All Rights Reserved',\n      }\n    }\n  };\n};\n\nconst mockSearchApi = async (request: SearchRequest): Promise<string[]> => {\n  await mockApiDelay();\n  \n  // Mock search suggestions based on query\n  const mockSuggestions = [\n    'dress', 'shirt', 'pants', 'shoes', 'jacket', 'skirt', 'blouse', 'jeans',\n    'sweater', 'coat', 'boots', 'sneakers', 'accessories', 'bag', 'hat'\n  ];\n  \n  if (!request.query) {\n    return [];\n  }\n  \n  return mockSuggestions.filter(suggestion => \n    suggestion.toLowerCase().includes(request.query.toLowerCase())\n  ).slice(0, 5);\n};\n\n// Saga workers\nfunction* initializeHomeSaga() {\n  try {\n    const data: { navigation: NavigationItem[], footer: FooterData } = yield call(mockInitializeHomeApi);\n    yield put(initializeHomeSuccess(data));\n  } catch (error) {\n    const apiError: ApiError = {\n      message: error instanceof Error ? error.message : 'Failed to initialize home page',\n      code: 'INIT_ERROR'\n    };\n    yield put(initializeHomeFailure(apiError));\n  }\n}\n\nfunction* submitSearchSaga(action: PayloadAction<SearchRequest>) {\n  try {\n    yield put(setSearching(true));\n    \n    // Add small delay for better UX\n    yield delay(300);\n    \n    const suggestions: string[] = yield call(mockSearchApi, action.payload);\n    yield put(submitSearchSuccess(suggestions));\n  } catch (error) {\n    const apiError: ApiError = {\n      message: error instanceof Error ? error.message : 'Search failed',\n      code: 'SEARCH_ERROR'\n    };\n    yield put(submitSearchFailure(apiError));\n  }\n}\n\n// Watcher sagas\nfunction* watchInitializeHome() {\n  yield takeEvery(initializeHome.type, initializeHomeSaga);\n}\n\nfunction* watchSubmitSearch() {\n  yield takeLatest(submitSearch.type, submitSearchSaga);\n}\n\n// Root saga for home feature\nexport function* homeSaga() {\n  yield takeEvery(initializeHome.type, initializeHomeSaga);\n  yield takeLatest(submitSearch.type, submitSearchSaga);\n}\n\nexport default homeSaga;\n"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,sDAAsD;;;;;AAEtD;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAgBA,mDAAmD;AACnD,MAAM,eAAe,IAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAEtE,MAAM,wBAAwB;IAC5B,MAAM;IAEN,0DAA0D;IAC1D,OAAO;QACL,YAAY;YACV;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,eAAe;oBACb;wBAAE,IAAI;wBAAS,OAAO;wBAAS,MAAM;oBAAc;oBACnD;wBAAE,IAAI;wBAAO,OAAO;wBAAO,MAAM;oBAAY;oBAC7C;wBAAE,IAAI;wBAAe,OAAO;wBAAe,MAAM;oBAAoB;iBACtE;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;QACD,QAAQ;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL;4BAAE,IAAI;4BAAoB,OAAO;4BAAoB,MAAM;wBAAgB;wBAC3E;4BAAE,IAAI;4BAAoB,OAAO;4BAAoB,MAAM;wBAAiB;wBAC5E;4BAAE,IAAI;4BAAoB,OAAO;4BAAsB,MAAM;wBAAc;wBAC3E;4BAAE,IAAI;4BAAkB,OAAO;4BAAkB,MAAM;wBAAgB;qBACxE;gBACH;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL;4BAAE,IAAI;4BAAW,OAAO;4BAAW,MAAM;wBAAe;wBACxD;4BAAE,IAAI;4BAAqB,OAAO;4BAAqB,MAAM;wBAAkB;wBAC/E;4BAAE,IAAI;4BAAU,OAAO;4BAAU,MAAM;wBAAc;wBACrD;4BAAE,IAAI;4BAAY,OAAO;4BAAY,MAAM;wBAAgB;qBAC5D;gBACH;aACD;YACD,aAAa;gBACX;oBAAE,IAAI;oBAAW,UAAU;oBAAW,MAAM;oBAAK,MAAM;gBAAU;gBACjE;oBAAE,IAAI;oBAAY,UAAU;oBAAY,MAAM;oBAAK,MAAM;gBAAW;gBACpE;oBAAE,IAAI;oBAAa,UAAU;oBAAa,MAAM;oBAAK,MAAM;gBAAY;gBACvE;oBAAE,IAAI;oBAAa,UAAU;oBAAa,MAAM;oBAAK,MAAM;gBAAY;aACxE;YACD,gBAAgB;gBACd;oBAAE,IAAI;oBAAQ,MAAM;oBAAQ,MAAM;gBAAO;gBACzC;oBAAE,IAAI;oBAAc,MAAM;oBAAc,MAAM;gBAAa;gBAC3D;oBAAE,IAAI;oBAAU,MAAM;oBAAU,MAAM;gBAAS;gBAC/C;oBAAE,IAAI;oBAAa,MAAM;oBAAa,MAAM;gBAAY;gBACxD;oBAAE,IAAI;oBAAc,MAAM;oBAAc,MAAM;gBAAa;aAC5D;YACD,aAAa;gBACX,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;QACF;IACF;AACF;AAEA,MAAM,gBAAgB,OAAO;IAC3B,MAAM;IAEN,yCAAyC;IACzC,MAAM,kBAAkB;QACtB;QAAS;QAAS;QAAS;QAAS;QAAU;QAAS;QAAU;QACjE;QAAW;QAAQ;QAAS;QAAY;QAAe;QAAO;KAC/D;IAED,IAAI,CAAC,QAAQ,KAAK,EAAE;QAClB,OAAO,EAAE;IACX;IAEA,OAAO,gBAAgB,MAAM,CAAC,CAAA,aAC5B,WAAW,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,KAC3D,KAAK,CAAC,GAAG;AACb;AAEA,eAAe;AACf,UAAU;IACR,IAAI;QACF,MAAM,OAA6D,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE;QAC9E,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,MAAM,WAAqB;YACzB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;QACR;QACA,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE;IAClC;AACF;AAEA,UAAU,iBAAiB,MAAoC;IAC7D,IAAI;QACF,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,eAAY,AAAD,EAAE;QAEvB,gCAAgC;QAChC,MAAM,CAAA,GAAA,+LAAA,CAAA,QAAK,AAAD,EAAE;QAEZ,MAAM,cAAwB,MAAM,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,OAAO;QACtE,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,MAAM,WAAqB;YACzB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;QACR;QACA,MAAM,CAAA,GAAA,6LAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;IAChC;AACF;AAEA,gBAAgB;AAChB,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,YAAS,AAAD,EAAE,6IAAA,CAAA,iBAAc,CAAC,IAAI,EAAE;AACvC;AAEA,UAAU;IACR,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,6IAAA,CAAA,eAAY,CAAC,IAAI,EAAE;AACtC;AAGO,UAAU;IACf,MAAM,CAAA,GAAA,kMAAA,CAAA,YAAS,AAAD,EAAE,6IAAA,CAAA,iBAAc,CAAC,IAAI,EAAE;IACrC,MAAM,CAAA,GAAA,kMAAA,CAAA,aAAU,AAAD,EAAE,6IAAA,CAAA,eAAY,CAAC,IAAI,EAAE;AACtC;uCAEe", "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/features/home/<USER>"], "sourcesContent": ["// Home Feature Barrel Export\n// Self-contained home module exports\n\n// Export containers\nexport { HomeContainer } from './containers/HomeContainer';\n\n// Export components\nexport { HomePresenter } from './components/HomePresenter';\nexport * from './components/Icons';\n\n// Export states\nexport { HomeCallState } from './states/HomeCallState';\n\n// Export redux\nexport { \n  homeReducer,\n  setActiveNavigation,\n  toggleMenu,\n  closeMenu,\n  updateSearch,\n  setSearching,\n  submitSearch,\n  submitSearchSuccess,\n  submitSearchFailure,\n  clearSearch,\n  setLoading,\n  setError,\n  clearError,\n  initializeHome,\n  initializeHomeSuccess,\n  initializeHomeFailure,\n  resetHomeState,\n  homeActionCreators,\n  selectHomeState,\n  selectNavigation,\n  selectSearch,\n  selectFooter,\n  selectIsLoading,\n  selectError,\n  selectIsInitialized,\n} from './redux/homeSlice';\n\nexport { homeSaga } from './redux/homeSaga';\n\n// Export types\nexport type {\n  NavigationItem,\n  NavigationState,\n  SearchState,\n  SearchRequest,\n  SearchFilters,\n  FooterLink,\n  FooterSection,\n  SocialLink,\n  PaymentMethod,\n  FooterData,\n  HomeState,\n  ApiError,\n  HomeContainerProps,\n  HomePresenterProps,\n  HomeCallStateProps,\n  SetActiveNavigationAction,\n  ToggleMenuAction,\n  UpdateSearchAction,\n  SubmitSearchAction,\n  ClearSearchAction,\n  SetLoadingAction,\n  SetErrorAction,\n  ClearErrorAction,\n  InitializeHomeAction,\n  InitializeHomeSuccessAction,\n  InitializeHomeFailureAction,\n  HomeAction,\n} from './types/home.types';\n\n// Default export\nexport { HomeContainer as default } from './containers/HomeContainer';\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,qCAAqC;AAErC,oBAAoB;;AACpB;AAEA,oBAAoB;AACpB;AACA;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf;AA4BA", "debugId": null}}, {"offset": {"line": 3304, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/store/rootReducer.ts"], "sourcesContent": ["import { combineReducers } from '@reduxjs/toolkit';\r\nimport { loginReducer } from '../features/auth/login';\r\nimport { homeReducer } from '../features/home';\r\n\r\nexport const rootReducer = combineReducers({\r\n  login: loginReducer,\r\n  home: homeReducer,\r\n});\r\n\r\nexport type RootState = ReturnType<typeof rootReducer>;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,cAAc,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD,EAAE;IACzC,OAAO,uJAAA,CAAA,eAAY;IACnB,MAAM,6IAAA,CAAA,cAAW;AACnB", "debugId": null}}, {"offset": {"line": 3323, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/store/sagas/rootSaga.ts"], "sourcesContent": ["import { all, fork } from 'redux-saga/effects';\r\nimport { loginSaga } from '../../features/auth/login';\r\nimport { homeSaga } from '../../features/home';\r\n\r\nexport function* rootSaga() {\r\n  yield all([\r\n    fork(loginSaga),\r\n    fork(homeSaga),\r\n  ]);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,UAAU;IACf,MAAM,CAAA,GAAA,8LAAA,CAAA,MAAG,AAAD,EAAE;QACR,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,sJAAA,CAAA,YAAS;QACd,CAAA,GAAA,8LAAA,CAAA,OAAI,AAAD,EAAE,4IAAA,CAAA,WAAQ;KACd;AACH", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport createSagaMiddleware from 'redux-saga';\nimport { rootReducer } from './rootReducer';\nimport { rootSaga } from './sagas/rootSaga';\n\n// Create saga middleware\nconst sagaMiddleware = createSagaMiddleware();\n\n// Configure store\nexport const store = configureStore({\n  reducer: rootReducer,\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      thunk: false, // Disable thunk since we're using saga\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n      },\n    }).concat(sagaMiddleware),\n  devTools: process.env.NODE_ENV !== 'production',\n});\n\n// Run saga middleware\nsagaMiddleware.run(rootSaga);\n\n// Export types\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n\n// Export store\nexport default store;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,yBAAyB;AACzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAoB,AAAD;AAGnC,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS,2HAAA,CAAA,cAAW;IACpB,YAAY,CAAC,uBACX,qBAAqB;YACnB,OAAO;YACP,mBAAmB;gBACjB,gBAAgB;oBAAC;oBAAmB;iBAAoB;YAC1D;QACF,GAAG,MAAM,CAAC;IACZ,UAAU,oDAAyB;AACrC;AAEA,sBAAsB;AACtB,eAAe,GAAG,CAAC,iIAAA,CAAA,WAAQ;uCAOZ", "debugId": null}}, {"offset": {"line": 3381, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Fashion/Frontend-Customer/src/providers/ReduxProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from '@/store';\n\ninterface ReduxProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {\n  return <Provider store={store}>{children}</Provider>;\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,qHAAA,CAAA,QAAK;kBAAG;;;;;;AAClC", "debugId": null}}]}
import { Banner, Product, ProductCategory } from '../types/home.types';

// Mock Banners
export const mockBanners: Banner[] = [
  {
    id: '1',
    title: 'SUMMER CLEARANCE SALE',
    subtitle: 'MLB',
    description: 'SALE UP TO 50%',
    image: '/images/banners/summer-sale.jpg',
    link: '/sale',
    buttonText: 'Shop Now',
    type: 'hero',
    isActive: true,
  },
  {
    id: '2',
    title: 'MLB SALE UP TO 50%',
    subtitle: 'MUA 2 GIẢM 10%\nMUA 3 GIẢM 15%',
    description: '26.6 - 13.7',
    image: '/images/banners/mlb-sale.jpg',
    link: '/mlb-sale',
    buttonText: 'Xem ngay',
    type: 'promotion',
    isActive: true,
  },
];

// Mock Product Categories
export const mockProductCategories: ProductCategory[] = [
  { id: 'clothes', name: 'CLOTHES', slug: 'clothes', isActive: true },
  { id: 'hat', name: 'H<PERSON>', slug: 'hat', isActive: true },
  { id: 'shoes', name: '<PERSON>OE<PERSON>', slug: 'shoes', isActive: true },
  { id: 'bag', name: '<PERSON><PERSON>', slug: 'bag', isActive: true },
];

// Mock New Arrivals
export const mockNewArrivals: Product[] = [
  {
    id: '1',
    name: 'Áo khoác bomber tổ phối oversize varsity',
    brand: 'MLB',
    price: 2890000,
    image: 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop&crop=center'],
    category: 'clothes',
    colors: ['black', 'white'],
    sizes: ['S', 'M', 'L', 'XL'],
    isNew: true,
    rating: 4.5,
    reviewCount: 128,
  },
  {
    id: '2',
    name: 'Áo thun unisex cổ tròn tay Faded Varsity',
    brand: 'MLB',
    price: 1590000,
    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center'],
    category: 'clothes',
    colors: ['beige', 'white'],
    sizes: ['S', 'M', 'L', 'XL'],
    isNew: true,
    rating: 4.3,
    reviewCount: 89,
  },
  {
    id: '3',
    name: 'Áo thun unisex cổ tròn tay ngắn varsity',
    brand: 'MLB',
    price: 1390000,
    image: 'https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center'],
    category: 'clothes',
    colors: ['black', 'white'],
    sizes: ['S', 'M', 'L', 'XL'],
    isNew: true,
    rating: 4.7,
    reviewCount: 156,
  },
  {
    id: '4',
    name: 'Áo sát nách nữ cổ tròn New York Yankees',
    brand: 'MLB',
    price: 1190000,
    image: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center'],
    category: 'clothes',
    colors: ['black', 'white'],
    sizes: ['S', 'M', 'L', 'XL'],
    isNew: true,
    rating: 4.4,
    reviewCount: 73,
  },
];

// Mock Recommended Products
export const mockRecommendedProducts: Product[] = [
  {
    id: '5',
    name: 'Nón Snapback phối unisex Kings New York Yankees',
    brand: 'MLB',
    price: 1290000,
    image: 'https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1588850561407-ed78c282e89b?w=400&h=400&fit=crop&crop=center'],
    category: 'hat',
    colors: ['green', 'black'],
    sizes: ['One Size'],
    rating: 4.6,
    reviewCount: 234,
  },
  {
    id: '6',
    name: 'Giày Sneakers unisex cổ thấp Chunky Runner Classic Monogram',
    brand: 'MLB',
    price: 3290000,
    image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop&crop=center'],
    category: 'shoes',
    colors: ['white', 'beige'],
    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],
    rating: 4.8,
    reviewCount: 312,
  },
  {
    id: '7',
    name: 'Nón bucket unisex Color Denim Unstructured',
    brand: 'MLB',
    price: 990000,
    image: 'https://images.unsplash.com/photo-1576871337622-98d48d1cf531?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1576871337622-98d48d1cf531?w=400&h=400&fit=crop&crop=center'],
    category: 'hat',
    colors: ['blue', 'black'],
    sizes: ['One Size'],
    rating: 4.2,
    reviewCount: 98,
  },
  {
    id: '8',
    name: 'Túi tote chữ kiểu unisex Mega Bear unstructured',
    brand: 'MLB',
    price: 1890000,
    image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop&crop=center'],
    category: 'bag',
    colors: ['brown', 'black'],
    sizes: ['One Size'],
    rating: 4.5,
    reviewCount: 167,
  },
  {
    id: '9',
    name: 'Dép quai ngang unisex New Monogram',
    brand: 'MLB',
    price: 1590000,
    image: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop&crop=center',
    images: ['https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop&crop=center'],
    category: 'shoes',
    colors: ['beige', 'black'],
    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],
    rating: 4.3,
    reviewCount: 145,
  },
];
